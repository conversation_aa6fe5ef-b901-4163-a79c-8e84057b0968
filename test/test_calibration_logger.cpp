#include <iostream>
#include <thread>
#include <chrono>
#include "CalibrationLogger.h"

using namespace tongxing;

// 自定义日志观察者，用于测试
class TestLogObserver : public CalibrationLogObserver {
public:
    void onSummaryLog(const CalibrationStats& stats, CalibrationPhase phase) override {
        std::cout << "=== 自定义观察者收到日志 ===" << std::endl;
        std::cout << "阶段: " << static_cast<int>(phase) << std::endl;
        std::cout << "总帧数: " << stats.total_frames << std::endl;
        std::cout << "接受帧数: " << stats.accepted_frames << std::endl;
        std::cout << "过滤帧数: " << stats.filtered_frames << std::endl;
        std::cout << "头部进度: " << stats.head_progress_percent << "%" << std::endl;
        std::cout << "左眼进度: " << stats.left_eye_progress_percent << "%" << std::endl;
        std::cout << "右眼进度: " << stats.right_eye_progress_percent << "%" << std::endl;
        
        if (!stats.filter_reasons.empty()) {
            std::cout << "过滤原因:" << std::endl;
            for (const auto& reason : stats.filter_reasons) {
                std::cout << "  - " << reason.first << ": " << reason.second << "次" << std::endl;
            }
        }
        
        if (stats.estimated_seconds_remaining > 0) {
            std::cout << "预计剩余时间: " << stats.estimated_seconds_remaining << "秒" << std::endl;
        }
        
        std::cout << "===========================" << std::endl;
    }
};

void simulateCalibrationProcess() {
    auto& logger = CalibrationLogger::getInstance();
    
    // 设置输出间隔为100ms，便于测试
    logger.setOutputInterval(100);
    
    // 添加控制台观察者和自定义观察者
    auto console_observer = std::make_shared<ConsoleLogObserver>();
    auto test_observer = std::make_shared<TestLogObserver>();
    logger.addObserver(console_observer);
    logger.addObserver(test_observer);
    
    // 重置统计信息
    logger.resetStats();
    
    // 模拟标定过程
    std::cout << "\n=== 模拟标定准备阶段 ===" << std::endl;
    logger.setPhase(CalibrationPhase::PREPARING);
    logger.recordVehicleInfo(0.0f, 0);
    
    // 模拟车速过低过滤
    for (int i = 0; i < 10; i++) {
        logger.recordFilter("车速过低", "车速" + std::to_string(i * 2) + "km/h低于20km/h");
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    
    // 模拟车速正常，开始标定
    std::cout << "\n=== 模拟标定进行阶段 ===" << std::endl;
    logger.setPhase(CalibrationPhase::IN_PROGRESS);
    logger.recordVehicleInfo(25.0f, 1);
    
    // 模拟缓冲区填充过程
    for (int i = 1; i <= 10; i++) {
        int head_buffer = i * 30;
        int left_eye_buffer = i * 28;
        int right_eye_buffer = i * 29;
        
        logger.recordProgress(head_buffer, left_eye_buffer, right_eye_buffer);
        
        // 偶尔添加一些过滤事件
        if (i % 3 == 0) {
            logger.recordFilter("人脸角度超范围", "yaw角度" + std::to_string(25 + i) + "超出配置范围");
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    // 模拟标定完成
    std::cout << "\n=== 模拟标定完成阶段 ===" << std::endl;
    logger.recordSuccess(true, true, true);
    
    // 强制输出最终统计信息
    logger.flushStats();
    
    // 等待一会，确保所有日志都已输出
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
}

void testFilterReasons() {
    auto& logger = CalibrationLogger::getInstance();
    
    // 重置统计信息
    logger.resetStats();
    
    std::cout << "\n=== 测试过滤原因统计 ===" << std::endl;
    
    // 模拟多种过滤原因
    logger.recordFilter("车速过低", "车速15km/h低于20km/h");
    logger.recordFilter("车速过低", "车速18km/h低于20km/h");
    logger.recordFilter("档位无效", "档位0不是前进档");
    logger.recordFilter("人脸角度超范围", "yaw角度25超出配置范围");
    logger.recordFilter("人脸角度超范围", "pitch角度30超出配置范围");
    logger.recordFilter("人脸角度超范围", "roll角度15超出配置范围");
    logger.recordFilter("方向盘角度无效", "方向盘转角0.8超出配置范围");
    
    // 强制输出统计信息
    logger.flushStats();
    
    // 等待一会，确保所有日志都已输出
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
}

int main() {
    std::cout << "=== 标定日志系统测试 ===" << std::endl;
    
    // 测试模拟标定过程
    simulateCalibrationProcess();
    
    // 测试过滤原因统计
    testFilterReasons();
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    return 0;
}
