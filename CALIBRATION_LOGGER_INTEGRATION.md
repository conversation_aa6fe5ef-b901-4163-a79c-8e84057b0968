# 标定日志系统集成指南

本文档提供了将CalibrationLogger集成到其他模块的详细步骤。

## 集成步骤

### 1. 包含头文件

在需要使用日志系统的文件中包含头文件：

```cpp
#include "CalibrationLogger.h"
```

### 2. 初始化日志系统

在模块初始化时，设置日志系统：

```cpp
void YourClass::initLogger() {
    auto& logger = tongxing::CalibrationLogger::getInstance();
    
    // 设置输出间隔（毫秒）
    logger.setOutputInterval(1000);
    
    // 启用日志（可以绑定到现有的log_switch）
    logger.setEnabled(true);
    
    // 添加控制台观察者
    auto console_observer = std::make_shared<tongxing::ConsoleLogObserver>();
    logger.addObserver(console_observer);
    
    // 重置统计信息
    logger.resetStats();
    
    // 设置初始阶段
    logger.setPhase(tongxing::CalibrationPhase::PREPARING);
}
```

### 3. 替换现有的调试输出

#### 3.1 替换进度输出

**旧代码：**
```cpp
std::cout << "[Debug]: 缓冲区: " << head_buffer << "/" << left_eye_buffer << "/" << right_eye_buffer << std::endl;
```

**新代码：**
```cpp
auto& logger = tongxing::CalibrationLogger::getInstance();
logger.recordProgress(head_buffer, left_eye_buffer, right_eye_buffer);
```

#### 3.2 替换过滤条件输出

**旧代码：**
```cpp
std::cout << "[Debug]: 过滤原因: 车速过低(" << car_speed << "km/h)" << std::endl;
```

**新代码：**
```cpp
auto& logger = tongxing::CalibrationLogger::getInstance();
logger.recordFilter("车速过低", "车速" + std::to_string(car_speed) + "km/h低于阈值");
```

#### 3.3 替换成功/失败输出

**旧代码：**
```cpp
std::cout << "[Debug]: 标定成功|头部:" << (head_complete ? "完成" : "未完成") << "|左眼:" << (left_eye_complete ? "完成" : "未完成") << std::endl;
```

**新代码：**
```cpp
auto& logger = tongxing::CalibrationLogger::getInstance();
logger.recordSuccess(head_complete, left_eye_complete, right_eye_complete);
```

#### 3.4 替换车辆信息输出

**旧代码：**
```cpp
std::cout << "[Debug]: 车速:" << car_speed << "|档位:" << gear << std::endl;
```

**新代码：**
```cpp
auto& logger = tongxing::CalibrationLogger::getInstance();
logger.recordVehicleInfo(car_speed, gear);
```

### 4. 更新标定阶段

在标定过程的不同阶段，更新阶段状态：

```cpp
// 准备阶段
logger.setPhase(tongxing::CalibrationPhase::PREPARING);

// 进行中
logger.setPhase(tongxing::CalibrationPhase::IN_PROGRESS);

// 完成
logger.setPhase(tongxing::CalibrationPhase::COMPLETED);

// 失败
logger.setPhase(tongxing::CalibrationPhase::FAILED);
```

### 5. 自定义观察者（可选）

如果需要将日志输出到文件或其他目标，可以创建自定义观察者：

```cpp
class FileLogObserver : public tongxing::CalibrationLogObserver {
public:
    FileLogObserver(const std::string& filename) {
        file_.open(filename, std::ios::out | std::ios::app);
    }
    
    ~FileLogObserver() {
        if (file_.is_open()) {
            file_.close();
        }
    }
    
    void onSummaryLog(const tongxing::CalibrationStats& stats, tongxing::CalibrationPhase phase) override {
        if (!file_.is_open()) return;
        
        // 格式化日志并写入文件
        file_ << "[" << getCurrentTimeString() << "] ";
        file_ << "阶段: " << getPhaseString(phase) << " | ";
        file_ << "进度: " << stats.head_progress_percent << "% | ";
        file_ << "帧数: " << stats.accepted_frames << "/" << stats.total_frames;
        file_ << std::endl;
    }
    
private:
    std::ofstream file_;
    
    std::string getCurrentTimeString() {
        auto now = std::chrono::system_clock::now();
        auto time = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time), "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }
    
    std::string getPhaseString(tongxing::CalibrationPhase phase) {
        switch (phase) {
            case tongxing::CalibrationPhase::PREPARING: return "准备中";
            case tongxing::CalibrationPhase::IN_PROGRESS: return "进行中";
            case tongxing::CalibrationPhase::COMPLETED: return "已完成";
            case tongxing::CalibrationPhase::FAILED: return "失败";
            default: return "未知";
        }
    }
};

// 添加文件观察者
auto file_observer = std::make_shared<FileLogObserver>("calibration.log");
logger.addObserver(file_observer);
```

## 集成示例

### 完整示例

```cpp
#include "CalibrationLogger.h"

class MyCalibrationModule {
public:
    MyCalibrationModule() {
        initLogger();
    }
    
    void initLogger() {
        auto& logger = tongxing::CalibrationLogger::getInstance();
        logger.setOutputInterval(1000);
        logger.setEnabled(true);
        
        auto console_observer = std::make_shared<tongxing::ConsoleLogObserver>();
        logger.addObserver(console_observer);
        
        logger.resetStats();
        logger.setPhase(tongxing::CalibrationPhase::PREPARING);
    }
    
    void processFrame(float car_speed, int gear, float head_yaw, float head_pitch) {
        auto& logger = tongxing::CalibrationLogger::getInstance();
        
        // 记录车辆信息
        logger.recordVehicleInfo(car_speed, gear);
        
        // 检查条件
        if (car_speed < 20.0f) {
            logger.recordFilter("车速过低", "车速" + std::to_string(car_speed) + "km/h低于20km/h");
            return;
        }
        
        if (gear != 1) {
            logger.recordFilter("档位无效", "档位" + std::to_string(gear) + "不是前进档");
            return;
        }
        
        // 更新阶段
        logger.setPhase(tongxing::CalibrationPhase::IN_PROGRESS);
        
        // 更新缓冲区
        head_buffer_++;
        left_eye_buffer_++;
        right_eye_buffer_++;
        
        // 记录进度
        logger.recordProgress(head_buffer_, left_eye_buffer_, right_eye_buffer_);
        
        // 检查是否完成
        if (head_buffer_ >= 300 && left_eye_buffer_ >= 280 && right_eye_buffer_ >= 280) {
            logger.setPhase(tongxing::CalibrationPhase::COMPLETED);
            logger.recordSuccess(true, true, true);
        }
    }
    
private:
    int head_buffer_ = 0;
    int left_eye_buffer_ = 0;
    int right_eye_buffer_ = 0;
};
```

## 常见问题

### Q: 如何在多线程环境中使用？
A: 当前实现不是线程安全的。如需在多线程环境使用，需要添加互斥锁保护共享数据。

### Q: 如何控制日志输出的详细程度？
A: 可以通过自定义观察者来控制输出的详细程度。在onSummaryLog方法中，根据需要选择性地输出部分信息。

### Q: 如何禁用日志输出？
A: 调用`logger.setEnabled(false)`可以完全禁用日志输出。

### Q: 如何更改输出格式？
A: 创建自定义的观察者，实现自己的onSummaryLog方法，按照需要的格式输出信息。

### Q: 如何添加新的统计指标？
A: 修改CalibrationStats结构体，添加新的统计字段，并在相应的record方法中更新这些字段。

## 最佳实践

1. **集中初始化**：在模块初始化时一次性设置日志系统
2. **适当的输出间隔**：根据实际需求设置合适的输出间隔，避免过多或过少的输出
3. **有意义的过滤原因**：使用简洁明了的过滤原因描述，便于问题诊断
4. **及时更新阶段**：在标定过程的不同阶段，及时更新阶段状态
5. **避免频繁获取实例**：缓存logger实例的引用，避免频繁调用getInstance()

## 注意事项

1. 日志系统使用单例模式，全局共享一个实例
2. 观察者使用shared_ptr管理，注意避免循环引用
3. 输出间隔是全局设置，会影响所有观察者
4. 在性能敏感的代码路径上，应谨慎使用日志系统
