#include "tongxing_util/src/util/CalibrationLogger.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <iomanip>
#include <sstream>

using namespace tongxing;

// 模拟旧系统的调试输出
void oldSystemOutput(int head_buffer, int left_eye_buffer, int right_eye_buffer, 
                     float car_speed, int gear, float head_yaw, float head_pitch, float head_roll,
                     float left_eye_conf, float right_eye_conf) {
    std::cout << "[Calibration Debug]: ";
    
    if (head_buffer >= 300 && left_eye_buffer >= 280 && right_eye_buffer >= 280) {
        std::cout << "DONE";
    } else {
        std::cout << "DOING(头部:" << (head_buffer >= 300 ? "满" : std::to_string(head_buffer) + "/300");
        std::cout << "|左眼:" << (left_eye_buffer >= 300 ? "满" : std::to_string(left_eye_buffer) + "/300");
        std::cout << "|右眼:" << (right_eye_buffer >= 300 ? "满" : std::to_string(right_eye_buffer) + "/300") << ")";
    }
    
    std::cout << "|车速:" << car_speed << "|档位:" << gear;
    std::cout << "|头部角度:" << head_yaw << "," << head_pitch << "," << head_roll;
    std::cout << "|眼部置信度:" << left_eye_conf << "," << right_eye_conf;
    std::cout << std::endl;
}

void oldSystemFilterOutput(const std::string& reason, const std::string& details) {
    std::cout << "[Calibration Debug]: CONDITION_UNDONE|原因:" << reason << "(" << details << ")" << std::endl;
}

void oldSystemCalibratorOutput(const std::string& message) {
    std::cout << "[Calibrator Debug]: " << message << std::endl;
}

// 获取当前时间字符串
std::string getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time), "%H:%M:%S");
    return ss.str();
}

// 模拟标定场景1：车速过低
void scenario1_LowSpeed() {
    std::cout << "\n=== 场景1：车速过低 ===" << std::endl;
    
    // 旧系统输出
    std::cout << "\n旧系统输出：" << std::endl;
    for (int i = 0; i < 3; i++) {
        float speed = 15.0f + i;
        oldSystemOutput(0, 0, 0, speed, 1, 5.2f, 3.1f, 1.8f, 1.2f, 1.1f);
        oldSystemFilterOutput("车速过低", std::to_string(speed) + "km/h");
    }
    
    // 新系统输出
    std::cout << "\n新系统输出：" << std::endl;
    auto& logger = CalibrationLogger::getInstance();
    logger.setOutputInterval(500);
    logger.setEnabled(true);
    
    // 清除之前的观察者
    auto console_observer = std::make_shared<ConsoleLogObserver>();
    logger.addObserver(console_observer);
    
    logger.resetStats();
    logger.setPhase(CalibrationPhase::PREPARING);
    
    for (int i = 0; i < 3; i++) {
        float speed = 15.0f + i;
        logger.recordVehicleInfo(speed, 1);
        logger.recordFilter("车速过低", "车速" + std::to_string(speed) + "km/h低于20km/h");
    }
    
    // 强制输出
    logger.flushStats();
    
    std::cout << std::endl;
}

// 模拟标定场景2：标定进行中
void scenario2_InProgress() {
    std::cout << "\n=== 场景2：标定进行中 ===" << std::endl;
    
    // 旧系统输出
    std::cout << "\n旧系统输出：" << std::endl;
    for (int i = 0; i < 3; i++) {
        int head_buffer = 120 + i;
        int left_eye_buffer = 115 + i;
        int right_eye_buffer = 118 + i;
        oldSystemOutput(head_buffer, left_eye_buffer, right_eye_buffer, 
                       25.0f, 1, 5.2f + i * 0.1f, 3.1f + i * 0.1f, 1.8f + i * 0.1f, 
                       1.2f + i * 0.1f, 1.1f + i * 0.1f);
        
        if (i == 0) {
            oldSystemCalibratorOutput("头部角度超出[-50°,50°]范围(55.2,3.1,1.8)");
        } else if (i == 1) {
            oldSystemCalibratorOutput("左眼角度超出[-50°,50°]范围(2.1,55.0)");
        }
    }
    
    // 新系统输出
    std::cout << "\n新系统输出：" << std::endl;
    auto& logger = CalibrationLogger::getInstance();
    logger.resetStats();
    logger.setPhase(CalibrationPhase::IN_PROGRESS);
    
    for (int i = 0; i < 3; i++) {
        int head_buffer = 120 + i;
        int left_eye_buffer = 115 + i;
        int right_eye_buffer = 118 + i;
        
        logger.recordProgress(head_buffer, left_eye_buffer, right_eye_buffer);
        logger.recordVehicleInfo(25.0f, 1);
        
        if (i == 0) {
            logger.recordFilter("头部角度超出合理范围", "角度(55.2,3.1,1.8)超出[-50°,50°]范围");
        } else if (i == 1) {
            logger.recordFilter("左眼角度超出合理范围", "角度(2.1,55.0)超出[-50°,50°]范围");
        }
    }
    
    // 强制输出
    logger.flushStats();
    
    std::cout << std::endl;
}

// 模拟标定场景3：标定完成
void scenario3_Completed() {
    std::cout << "\n=== 场景3：标定完成 ===" << std::endl;
    
    // 旧系统输出
    std::cout << "\n旧系统输出：" << std::endl;
    oldSystemOutput(300, 285, 295, 30.0f, 1, 2.1f, 1.5f, 0.8f, 1.3f, 1.2f);
    std::cout << "[Calibration Debug]: 标定成功|头部:完成|左眼:完成|右眼:完成|融合眼:右眼" << std::endl;
    oldSystemCalibratorOutput("标定完成|头部:300|左眼:285|右眼:295");
    
    // 新系统输出
    std::cout << "\n新系统输出：" << std::endl;
    auto& logger = CalibrationLogger::getInstance();
    logger.resetStats();
    
    logger.recordProgress(300, 285, 295);
    logger.recordVehicleInfo(30.0f, 1);
    logger.setPhase(CalibrationPhase::COMPLETED);
    logger.recordSuccess(true, true, true);
    
    // 强制输出
    logger.flushStats();
    
    std::cout << std::endl;
}

// 模拟标定场景4：多种过滤原因
void scenario4_MultipleFilters() {
    std::cout << "\n=== 场景4：多种过滤原因 ===" << std::endl;
    
    // 旧系统输出
    std::cout << "\n旧系统输出：" << std::endl;
    oldSystemFilterOutput("车速过低", "15km/h");
    oldSystemFilterOutput("车速过低", "18km/h");
    oldSystemFilterOutput("档位无效", "0");
    oldSystemFilterOutput("人脸角度超范围", "yaw:25");
    oldSystemFilterOutput("人脸角度超范围", "pitch:30");
    oldSystemFilterOutput("人脸角度超范围", "roll:15");
    oldSystemFilterOutput("方向盘角度无效", "0.8");
    
    // 新系统输出
    std::cout << "\n新系统输出：" << std::endl;
    auto& logger = CalibrationLogger::getInstance();
    logger.resetStats();
    
    logger.recordFilter("车速过低", "车速15km/h低于20km/h");
    logger.recordFilter("车速过低", "车速18km/h低于20km/h");
    logger.recordFilter("档位无效", "档位0不是前进档");
    logger.recordFilter("人脸角度超范围", "yaw角度25超出配置范围");
    logger.recordFilter("人脸角度超范围", "pitch角度30超出配置范围");
    logger.recordFilter("人脸角度超范围", "roll角度15超出配置范围");
    logger.recordFilter("方向盘角度无效", "方向盘转角0.8超出配置范围");
    
    // 强制输出
    logger.flushStats();
    
    std::cout << std::endl;
}

// 性能测试
void performanceTest() {
    std::cout << "\n=== 性能测试 ===" << std::endl;
    
    const int ITERATIONS = 1000;
    
    // 旧系统性能测试
    std::cout << "\n旧系统性能测试 (" << ITERATIONS << "次输出):" << std::endl;
    auto old_start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < ITERATIONS; i++) {
        std::stringstream ss;
        ss << "[Calibration Debug]: DOING(头部:" << i % 300 << "/300|左眼:" << i % 280 
           << "/300|右眼:" << i % 290 << "/300)|车速:25|档位:1|头部角度:5.2,3.1,1.8|眼部置信度:1.2,1.1";
        std::string output = ss.str();
        // 注释掉实际输出，只测试字符串操作性能
        // std::cout << output << std::endl;
    }
    
    auto old_end = std::chrono::high_resolution_clock::now();
    auto old_duration = std::chrono::duration_cast<std::chrono::microseconds>(old_end - old_start).count();
    
    // 新系统性能测试
    std::cout << "新系统性能测试 (" << ITERATIONS << "次记录):" << std::endl;
    auto& logger = CalibrationLogger::getInstance();
    logger.setOutputInterval(1000000); // 设置超大间隔，避免中间输出
    logger.resetStats();
    
    auto new_start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < ITERATIONS; i++) {
        logger.recordProgress(i % 300, i % 280, i % 290);
        logger.recordVehicleInfo(25.0f, 1);
    }
    
    auto new_end = std::chrono::high_resolution_clock::now();
    auto new_duration = std::chrono::duration_cast<std::chrono::microseconds>(new_end - new_start).count();
    
    std::cout << "旧系统耗时: " << old_duration << " 微秒" << std::endl;
    std::cout << "新系统耗时: " << new_duration << " 微秒" << std::endl;
    std::cout << "性能比例: " << (double)new_duration / old_duration << "x" << std::endl;
    
    // 恢复正常间隔
    logger.setOutputInterval(500);
}

int main() {
    std::cout << "=== 标定日志系统新旧对比测试 ===" << std::endl;
    std::cout << "时间: " << getCurrentTimeString() << std::endl;
    
    // 场景1：车速过低
    scenario1_LowSpeed();
    
    // 场景2：标定进行中
    scenario2_InProgress();
    
    // 场景3：标定完成
    scenario3_Completed();
    
    // 场景4：多种过滤原因
    scenario4_MultipleFilters();
    
    // 性能测试
    performanceTest();
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    std::cout << "时间: " << getCurrentTimeString() << std::endl;
    
    return 0;
}
