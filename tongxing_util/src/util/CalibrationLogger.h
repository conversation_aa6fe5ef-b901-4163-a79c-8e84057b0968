#pragma once

#include <string>
#include <vector>
#include <chrono>
#include <functional>
#include <memory>
#include <map>

namespace tongxing {

// 标定事件类型
enum class CalibrationEventType {
    PROGRESS,       // 进度更新
    FILTER,         // 过滤事件
    SUCCESS,        // 标定成功
    FAILURE,        // 标定失败
    INFO            // 一般信息
};

// 标定阶段
enum class CalibrationPhase {
    PREPARING,      // 准备阶段
    IN_PROGRESS,    // 进行中
    COMPLETED,      // 已完成
    FAILED          // 失败
};

// 标定事件结构
struct CalibrationEvent {
    CalibrationEventType type;
    std::string message;
    std::chrono::system_clock::time_point timestamp;
    
    // 附加数据
    struct {
        int head_buffer_size = 0;
        int left_eye_buffer_size = 0;
        int right_eye_buffer_size = 0;
        bool head_complete = false;
        bool left_eye_complete = false;
        bool right_eye_complete = false;
        std::string filter_reason;
        float vehicle_speed = 0.0f;
        int vehicle_gear = 0;
        CalibrationPhase phase = CalibrationPhase::PREPARING;
    } data;
};

// 标定统计信息
struct CalibrationStats {
    int total_frames = 0;
    int accepted_frames = 0;
    int filtered_frames = 0;
    std::map<std::string, int> filter_reasons;
    std::chrono::system_clock::time_point start_time;
    
    // 缓冲区进度
    int head_progress_percent = 0;
    int left_eye_progress_percent = 0;
    int right_eye_progress_percent = 0;
    
    // 预计完成时间
    int estimated_seconds_remaining = -1;
    
    CalibrationStats() {
        start_time = std::chrono::system_clock::now();
    }
};

// 标定日志观察者接口
class CalibrationLogObserver {
public:
    virtual ~CalibrationLogObserver() = default;
    virtual void onSummaryLog(const CalibrationStats& stats, CalibrationPhase phase) = 0;
};

// 标定日志类 - 实现"少而精"的信息展示
class CalibrationLogger {
public:
    static CalibrationLogger& getInstance();
    
    // 禁止拷贝和移动
    CalibrationLogger(const CalibrationLogger&) = delete;
    CalibrationLogger& operator=(const CalibrationLogger&) = delete;
    
    // 添加/移除观察者
    void addObserver(std::shared_ptr<CalibrationLogObserver> observer);
    void removeObserver(std::shared_ptr<CalibrationLogObserver> observer);
    
    // 记录事件 - 这些方法只收集数据，不立即输出
    void recordProgress(int head_buffer, int left_eye_buffer, int right_eye_buffer);
    void recordFilter(const std::string& reason, const std::string& details);
    void recordSuccess(bool head_complete, bool left_eye_complete, bool right_eye_complete);
    void recordFailure(const std::string& reason);
    void recordVehicleInfo(float speed, int gear);
    
    // 设置输出间隔
    void setOutputInterval(long interval_ms);
    
    // 启用/禁用日志
    void setEnabled(bool enabled);
    
    // 强制输出当前统计信息
    void flushStats();
    
    // 重置统计信息
    void resetStats();
    
    // 更新标定阶段
    void setPhase(CalibrationPhase phase);

private:
    CalibrationLogger();
    
    // 检查是否应该输出汇总信息
    void checkAndOutputSummary();
    
    // 通知所有观察者
    void notifyObservers();
    
    // 计算预计完成时间
    void calculateEstimatedTime();
    
    // 更新进度百分比
    void updateProgressPercent();
    
    std::vector<std::shared_ptr<CalibrationLogObserver>> observers_;
    std::chrono::system_clock::time_point last_output_time_;
    long output_interval_ms_;
    bool enabled_;
    
    // 统计信息
    CalibrationStats current_stats_;
    CalibrationPhase current_phase_;
    
    // 最近的车辆信息
    float last_vehicle_speed_ = 0.0f;
    int last_vehicle_gear_ = 0;
    
    // 目标缓冲区大小
    static constexpr int TARGET_BUFFER_SIZE = 300;
};

// 控制台输出观察者 - 实现清晰的汇总信息格式
class ConsoleLogObserver : public CalibrationLogObserver {
public:
    void onSummaryLog(const CalibrationStats& stats, CalibrationPhase phase) override;
    
private:
    std::string formatPhase(CalibrationPhase phase);
    std::string formatProgress(int percent);
    std::string formatTime(int seconds);
};

} // namespace tongxing
