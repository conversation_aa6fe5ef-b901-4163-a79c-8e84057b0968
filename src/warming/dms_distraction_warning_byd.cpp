#include "dms_distraction_warning_byd.h"
#include <math.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <algorithm>
#include <fstream>
#include "CalmCarLog.h"
#include "cc_resource_register.h"
#include "json.h"
#include "svm_model.h"

int WINDOW_TIME;                //分神时间窗
float DISTRACTION_THR;          //分神阈值
int HEAD_YAW_BIAS_WINDOW_TIME;  //head_yaw_bias时间窗

float HEADPOSE_PITCH_THR;  //头部姿态pitch 阈值
float GAZE_PITCH_THR;      //视线 pitch阈值

float HEADPOSE_YAW_THR;  //头部姿态yaw 阈值
float GAZE_YAW_THR;      //视线 yaw阈值

float PITCH_UP;    //临时过滤低头误报闭眼pitch
float PITCH_DOWN;  //临时过滤低头误报闭眼pitch
float YAW_LEFT;    //临时过滤偏头误报闭眼yaw偏差值
float YAW_RIGHT;   //临时过滤偏头误报闭眼yaw偏差值
float ROLL_LIFT;   //临时过滤偏头误报分神roll
float ROLL_RIGHT;  //临时过滤偏头误报分神roll

float STEERING_WHEEL_ANGLE_MIN;  //标定方向盘转角最小值
float STEERING_WHEEL_ANGLE_MAX;  //标定方向盘转角最大值

float CALIBRATE_HEADPOSE_YAW_NORMAL_MIN;    //标定人脸角度yaw最小值
float CALIBRATE_HEADPOSE_YAW_NORMAL_MAX;    //标定人脸角度yaw最大值
float CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN;  //标定人脸角度pitch最小值
float CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX;  //标定人脸角度pitch最大值
float CALIBRATE_HEADPOSE_ROLL_NORMAL_MIN;   //标定人脸角度roll最小值
float CALIBRATE_HEADPOSE_ROLL_NORMAL_MAX;   //标定人脸角度roll最大值

float HEADPOSE_YAW_NORMAL_MIN;    //正常人脸角度yaw最小值
float HEADPOSE_YAW_NORMAL_MAX;    //正常人脸角度yaw最大值
float HEADPOSE_PITCH_NORMAL_MIN;  //正常人脸角度pitch最小值
float HEADPOSE_PITCH_NORMAL_MAX;  //正常人脸角度pitch最大值
float HEADPOSE_ROLL_NORMAL_MIN;   //正常人脸角度roll最小值
float HEADPOSE_ROLL_NORMAL_MAX;   //正常人脸角度roll最大值

float HEAD_POSE_YAW_L;    //head pose yaw角 左方向绝对偏差值
float HEAD_POSE_YAW_R;    //head pose yaw角 右方向绝对偏差值
float HEAD_POSE_PITCH_U;  //head pose pitch 上方向绝对偏差值
float HEAD_POSE_PITCH_D;  //head pose pitch 下方向绝对偏差值

float HEAD_POSE_SPE_GLASSES_YAW_L;  //戴眼镜且眼睛不可见时设置的更多偏差
float HEAD_POSE_SPE_GLASSES_YAW_R;
float HEAD_POSE_SPE_GLASSES_PITCH_U;
float HEAD_POSE_SPE_GLASSES_PITCH_D;

float HEAD_YAW_L_OFFSET;  //视线融合头部姿态偏差值(用于判断是否优先使用视线融合做分心判断)
float HEAD_YAW_R_OFFSET;  //视线融合头部姿态偏差值(用于判断是否优先使用视线融合做分心判断)

float RIGHTEYE_UP_DOWN_PROPORTION;  //视线往下看,右眼绝对值
float LEFTEYE_UP_DOWN_PROPORTION;   //视线往下看,左眼绝对值

float HEAD_YAW_BIAS;  //头部yaw最大最小值的绝对值，用于辅助判断是否注释一个点位，延迟报警

int FUSION_USE_EYE;                                  //融合角度计算所使用的眼睛
int REGION_MAPPING_WIDTH;                            //区域映射宽度
int REGION_MAPPING_HEIGHT;                           //区域映射高度
float TOLERATE_PERCENTAGE;                           //容忍度百分比
std::vector<std::vector<cv::Point2f>> REGION_HULLS;  //区域轮廓点集

namespace tongxing {
DistractionWarn::DistractionWarn() {
    distraction_short_result.clear();
    distraction_long_result.clear();
    distraction_3s_result.clear();
    head_yaw_3s_vec.clear();
    glasses_vec.clear();
    mask_vec.clear();
    // temp_vec.clear();
    facevalid_mix_distra.clear();

    // 初始化眼睑曲率状态管理变量
    continuous_abnormal_count_ = 0;
    continuous_normal_count_ = 0;
    eye_curve_active_ = false;

    // 初始化标定日志系统
    initCalibrationLogger();

    //解析分神内置json
    Json::Reader config_json_reader;
    Json::Value config_root;
    auto config_json_data = CcResourcDataRegister::instance().get_function("sight.json");

    std::string config_doc = std::string((char*)config_json_data.second, config_json_data.first);
    if (!config_json_reader.parse(config_doc, config_root)) {
        TX_LOG_FATAL("TX DMS", "DistractionWarn Parse json config file failed!");
        // return -1;
    }
    //分神参数解析如下
    HEADPOSE_PITCH_THR = config_root["headpose_pitch_threshold"].asDouble();
    GAZE_PITCH_THR = config_root["gaze_pitch_threshold"].asDouble();
    HEADPOSE_YAW_THR = config_root["headpose_yaw_threshold"].asDouble();
    GAZE_YAW_THR = config_root["gaze_yaw_threshold"].asDouble();

    WINDOW_TIME = config_root["window_time"].asInt();
    DISTRACTION_THR = config_root["distraction_thr"].asDouble();
    HEAD_YAW_BIAS_WINDOW_TIME = config_root["head_yaw_bias_window_time"].asInt();

    CALIBRATE_HEADPOSE_YAW_NORMAL_MIN = config_root["calibrate_headpose_yaw_min"].asDouble();
    CALIBRATE_HEADPOSE_YAW_NORMAL_MAX = config_root["calibrate_headpose_yaw_max"].asDouble();
    CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN = config_root["calibrate_headpose_pitch_min"].asDouble();
    CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX = config_root["calibrate_headpose_pitch_max"].asDouble();
    CALIBRATE_HEADPOSE_ROLL_NORMAL_MIN = config_root["calibrate_headpose_roll_min"].asDouble();
    CALIBRATE_HEADPOSE_ROLL_NORMAL_MAX = config_root["calibrate_headpose_roll_max"].asDouble();

    PITCH_DOWN = config_root["pitch_down"].asDouble();
    PITCH_UP = config_root["pitch_up"].asDouble();
    YAW_LEFT = config_root["yaw_left"].asDouble();
    YAW_RIGHT = config_root["yaw_right"].asDouble();
    ROLL_LIFT = config_root["roll_left"].asDouble();
    ROLL_RIGHT = config_root["roll_right"].asDouble();

    STEERING_WHEEL_ANGLE_MIN = config_root["steering_wheel_angle_min"].asDouble();
    STEERING_WHEEL_ANGLE_MAX = config_root["steering_wheel_angle_max"].asDouble();

    HEADPOSE_YAW_NORMAL_MIN = config_root["headpose_yaw_normal_min"].asDouble();
    HEADPOSE_YAW_NORMAL_MAX = config_root["headpose_yaw_normal_max"].asDouble();
    HEADPOSE_PITCH_NORMAL_MIN = config_root["headpose_pitch_normal_min"].asDouble();
    HEADPOSE_PITCH_NORMAL_MAX = config_root["headpose_pitch_normal_max"].asDouble();
    HEADPOSE_ROLL_NORMAL_MIN = config_root["headpose_roll_normal_min"].asDouble();
    HEADPOSE_ROLL_NORMAL_MAX = config_root["headpose_roll_normal_max"].asDouble();

    HEAD_POSE_YAW_L = config_root["head_pose_yaw_left_offset"].asDouble();
    HEAD_POSE_YAW_R = config_root["head_pose_yaw_right_offset"].asDouble();
    HEAD_POSE_PITCH_U = config_root["head_pose_pitch_up_offset"].asDouble();
    HEAD_POSE_PITCH_D = config_root["head_pose_pitch_down_offset"].asDouble();
    HEAD_POSE_SPE_GLASSES_YAW_L = config_root["head_pose_spe_glasses_yaw_left_offset"].asDouble();
    HEAD_POSE_SPE_GLASSES_YAW_R = config_root["head_pose_spe_glasses_yaw_right_offset"].asDouble();
    HEAD_POSE_SPE_GLASSES_PITCH_U = config_root["head_pose_spe_glasses_pitch_up_offset"].asDouble();
    HEAD_POSE_SPE_GLASSES_PITCH_D = config_root["head_pose_spe_glasses_pitch_down_offset"].asDouble();

    HEAD_YAW_L_OFFSET = config_root["headgaze_yaw_l_offset"].asDouble();
    HEAD_YAW_R_OFFSET = config_root["headgaze_yaw_r_offset"].asDouble();

    RIGHTEYE_UP_DOWN_PROPORTION = config_root["righteye_up_down_proportion"].asDouble();
    LEFTEYE_UP_DOWN_PROPORTION = config_root["lefteye_up_down_proportion"].asDouble();

    HEAD_YAW_BIAS = config_root["head_yaw_bias"].asDouble();

    if (config_root.isMember("fusion_use_eye")) {
        FUSION_USE_EYE = config_root["fusion_use_eye"].asInt();
    }
    if (config_root.isMember("region_mapping_width")) {
        REGION_MAPPING_WIDTH = config_root["region_mapping_width"].asInt();
    }
    if (config_root.isMember("region_mapping_height")) {
        REGION_MAPPING_HEIGHT = config_root["region_mapping_height"].asInt();
    }
    if (config_root.isMember("tolerate_percentage")) {
        TOLERATE_PERCENTAGE = config_root["tolerate_percentage"].asFloat();
    }
    if (config_root.isMember("region_hull")) {
        cc_assert(config_root["region_hull"].isArray());
        std::vector<cv::Point2f> hull;
        REGION_HULLS.clear();
        for (int k = 0; k < config_root["region_hull"].size(); k++) {
            hull.clear();
            for (int i = 0; i < config_root["region_hull"][k].size(); i++) {
                hull.clear();
                for (int j = 0; j < config_root["region_hull"][k][i].size(); j += 2) {
                    cc_assert(config_root["region_hull"][k][i].size() % 2 == 0);

                    cv::Point2f point;
                    point.x = config_root["region_hull"][k][i][j].asFloat();
                    point.y = config_root["region_hull"][k][i][j + 1].asFloat();
                    hull.push_back(point);
                }
                // std::cout << "hull.size():" << hull.size() << std::endl;
                REGION_HULLS.push_back(hull);
            }
            std::cout << "use inside config json,REGION_HULLS.size():" << REGION_HULLS.size()
                      << std::endl;
        }
    }

    //标定参数如下
    int queue_length = 300;//500;
    int k_num = 1;               //test
    float cluster_radius = 5.0;//3.0;  //5.0;
    float threshold_100f = 0.8;
    float threshold_200f = 0.5;
    float threshold_longerf = 0.35;  //0.55;

    float eye_cluster_radius = 1.6;  //2.0;
    float eye_threshold_100f = 0.7;
    float eye_threshold_200f = 0.55;     //0.6;
    float eye_threshold_longerf = 0.45;  //0.5;

    int buffer_start_frame = 50;
    calibrator.reset(new HeadPoseCalibrator(
        queue_length, buffer_start_frame, k_num, cluster_radius, threshold_100f, threshold_200f, threshold_longerf,
        eye_cluster_radius, eye_threshold_100f, eye_threshold_200f, eye_threshold_longerf));
    calibrator->init();
    // 自定义标定模块时间跨度
    calibrator->setTimeFrameThresholds(50, 100, 150);

    // 快速标定函数，用于对齐实车环境快速debug
    QuickFillingCaliData();
}

DistractionWarn::~DistractionWarn() {}

void DistractionWarn::Update(const Distraction_Info& info) {
    std::pair<long, bool> temp_pair;
    temp_pair.first = info.time_input;
    temp_pair.second = info.distraction_status;

    distraction_long_result.emplace_back(temp_pair);
    if (auto_calibration) {
        distraction_short_result.emplace_back(temp_pair);
        if (distraction_short_result.size() >= 2) {
            if ((distraction_short_result.back().first - distraction_short_result.front().first) >
                31000) {
                distraction_short_result.pop_front();  //移除队列最前面的数据
            }
        }
    }
    if (distraction_long_result.size() >= 2) {
        if ((distraction_long_result.back().first - distraction_long_result.front().first) > 6000) {
            distraction_long_result.pop_front();  //移除队列最前面的数据
        }
    }

    distraction_3s_result.clear();
    GetDataQueue(distraction_long_result, distraction_3s_result, window_time);  //获取3.5s的数据

    cache_time = info.time_input;

    return;
}

void DistractionWarn::GetDataQueue(std::deque<std::pair<long, bool>>& input_distraction_deque,
                                   std::deque<std::pair<long, bool>>& out_distraction_deque,
                                   long time_gap) {
    if (input_distraction_deque.size() == 0) {
        return;
    }
    auto last_ts = input_distraction_deque.back();
    for (const auto& v : input_distraction_deque) {
        if (last_ts.first - v.first > time_gap)
            continue;
        out_distraction_deque.emplace_back(v);
    }
    return;
}

//这里计算各个报警状态

//计算是否持续分神
bool DistractionWarn::GetResult(std::deque<std::pair<long, bool>>& distraction_deque,
                                long time_gap) {
    // std::cout << "diff:" << distraction_deque.back().first- distraction_deque.front().first << " " << distraction_deque.back().first <<
    // " " << distraction_deque.front().first << " time_gap:" << time_gap << std::endl;
    if ((distraction_deque.back().first - distraction_deque.front().first) < time_gap)
        return false;

    int count = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == true)
            count++;
    }
    float thf = count * 1.0f / distraction_deque.size();
    bool bRet = (thf >= distraction_thr) && (distraction_deque.back().second) && (distraction_deque.front().second);
    // std::cout << "thf:" << thf << " distraction_thr:" << distraction_thr << " count:" << count << " distraction_deque.size():" << distraction_deque.size() << std::endl;
    return bRet;
}

bool DistractionWarn::GetMixDistraStatus(bool isFaceValid,
                                         const float face_keypoints_score,
                                         const TXCarInfo* car_info,
                                         bool camera_occlusion,
                                         bool distraction_status,
                                         long now_ts) {
    const size_t min_distraction_frames_to_trigger_no_face =
        2;  // 进行无人脸帧统计前面需要的分心帧数

    bool mixdistra_status = false, is_trigger = false;  //后2帧不是人脸不分心则触发
    float mix_distra_ratio = 0.0;
    int count = 0;

    int mixstatus =
        (!isFaceValid) ? 0 : (!distraction_status ? 1 : 2);  //0:无人脸;1:人脸不分心;2:人脸分心

    if (car_info->speed < 20)
        mixstatus = 1;  //速度小于20km/h，不分心

    // 记录无人脸帧前分心帧的具体分心原因
    if (mixstatus == 2 && headpose_reason.find("headyaw maxlimit") == std::string::npos &&
        headpose_reason.find("headyaw minlimit") == std::string::npos) {
        mixstatus = 0;  // 分心不是因为头部yaw触发的，则不视为分心的统计
    }
    // if (mixstatus == 1 && face_keypoints_score < kFaceAngleThreshold && face_keypoints_score > 0) {
    //     mixstatus =
    //         2;  // 将判断分心主流程中的人脸关键点置信度比较低的情况，移到混合统计分心+无人脸的函数中
    // }
    if (mixstatus == 1 && (face_box_ratio_ > 0.0 && face_box_ratio_ < 0.6)) {
        mixstatus =
            2;  // 将人脸框宽高比异常的情况，移到混合统计分心+无人脸的函数中
    }

    // std::cout << "face_keypoints_score:" << face_keypoints_score << " mixstatus:" << mixstatus << std::endl;
    std::pair<long, int> tmp_pair = {now_ts, mixstatus};
    facevalid_mix_distra.emplace_back(tmp_pair);

    if (facevalid_mix_distra.size() >= 2) {
        size_t data_size = facevalid_mix_distra.size();
        if ((facevalid_mix_distra[data_size - 1].second == 1) &&
            (facevalid_mix_distra[data_size - 2].second == 1)) {
            is_trigger = false;
            count = 0;
            facevalid_mix_distra.clear();
        }
    }

    if (car_info->speed >= 20 && !camera_occlusion && facevalid_mix_distra.size() >= 3) {
        if (facevalid_mix_distra.back().first - facevalid_mix_distra.front().first >= window_time) {
            size_t data_size = facevalid_mix_distra.size();
            for (size_t i = 0; i < data_size; i++) {
                int status = facevalid_mix_distra[i].second;
                if (!is_trigger) {
                    if (status == 2) {
                        count++;
                    } else {
                        count = 0;
                    }
                    if (count >= min_distraction_frames_to_trigger_no_face)
                        is_trigger = true;
                } else {
                    if (status != 1)
                        count++;
                }
            }
            facevalid_mix_distra.pop_front();
        }

        if (facevalid_mix_distra.size() > 0)
            mix_distra_ratio = (float)count / (float)facevalid_mix_distra.size();
    }

    if (mix_distra_ratio >= 0.7 && is_trigger) {
        mixdistra_status = true;
    }
    // std::cout << "mixdistra_status:" << mixdistra_status << " is_trigger:" << is_trigger << " mix_distra_ratio:" << mix_distra_ratio << std::endl;
    // std::cout << "count:" << count << " facevalid_mix_distra.size():" << facevalid_mix_distra.size()  << " mixstatus:" << mixstatus << std::endl;
    return mixdistra_status;
}

//计算短时分心，累计分神
bool DistractionWarn::GetShortTimeResult(std::deque<std::pair<long, bool>>& distraction_deque,
                                         long interval_times) {
    if (distraction_deque.size() == 0)
        return false;
    // if ((distraction_deque.back().first - distraction_deque.front().first) < 30000)
    //     return false;

    long start_time = -1;  // -1 表示未开始计算
    long total_true_time = 0;
    for (size_t i = 0; i < distraction_deque.size(); ++i) {
        long timestamp = distraction_deque[i].first;
        bool is_true = distraction_deque[i].second;

        if (is_true) {
            if (start_time == -1) {
                start_time = timestamp;  // 记录一个新的连续 true 段落的起始时间
            }
        } else {
            if (start_time != -1) {
                // 遇到 false，结束当前连续 true 段落的计算
                total_true_time += distraction_deque[i - 1].first - start_time;
                start_time = -1;  // 重置为未开始
            }
        }

        if (total_true_time >= interval_times) {
            return true;
        }
    }

    // 如果最后一个段落是 true，且没有遇到 false
    if (start_time != -1) {
        total_true_time += distraction_deque.back().first - start_time;
    }

    if (total_true_time >= interval_times) {
        return true;
    }

    std::deque<std::pair<long, bool>> temp_deque2;
    temp_deque2.clear();
    long cost_time2 = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == true) {
            temp_deque2.clear();
            cost_time2 = 0;
        } else {
            //正常驾驶区域持续时长≥2s,清空短时分心数据
            temp_deque2.emplace_back(v);
            if (temp_deque2.size() >= 2) {
                cost_time2 = temp_deque2.back().first - temp_deque2.front().first;
            }
            if (cost_time2 >= 2000) {
                distraction_deque.clear();
                return false;
            }
        }
    }
    return false;
}

//降级根据连续帧数计算
bool DistractionWarn::GetDegradeResult2(std::deque<std::pair<long, bool>>& distraction_deque,
                                        int cnt) {
    int count = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == false) {
            // return false;
            count++;

        } else {
            count = 0;
        }

        if (count >= cnt)
            return true;
    }
    return false;
}

long DistractionWarn::GetContinueDistractionTime(
    const std::deque<std::pair<long, bool>> distraction_deque) {
    if (distraction_deque.size() == 0)
        return 0;

    long current_consecutive_time = 0;
    int cnt = 0;

    for (size_t i = 1; i < distraction_deque.size(); ++i) {
        long time_diff = distraction_deque[i].first - distraction_deque[i - 1].first;

        if (distraction_deque[i].second == true) {
            if (distraction_deque[i - 1].second == true) {
                current_consecutive_time += time_diff;
                // cnt = 0;  // Reset count as the streak continues.
            } else {
                // Previous frame was false, but current is true, reset the counter.
                cnt = 0;
                current_consecutive_time += time_diff;
            }
        } else {
            // Current frame is false, increment the non-continuous count.
            cnt++;
            // Only reset `current_consecutive_time` if the count of non-continuous frames exceeds the threshold.
            if (cnt >= 1) {
                current_consecutive_time = 0;
            }
        }

        // distraction_info.distraction_continue_time = current_consecutive_time;
    }
    return current_consecutive_time;
}

float DistractionWarn::GetContinueDistractionPercent(
    const std::deque<std::pair<long, bool>> distraction_deque, long time_gap) {
    // if (distraction_deque.size() == 0 ||
    //     (distraction_deque.back().first - distraction_deque.front().first) < time_gap)
    if (distraction_deque.size() < 2)
        return 0.0f;

    int count = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == true)
            count++;
    }
    // return true;
    float thf = count * 1.0f / distraction_deque.size();

    return thf;
}

long DistractionWarn::GetSumDistractionTime(
    const std::deque<std::pair<long, bool>> distraction_deque) {
    long distraction_sum_time = 0;

    if (distraction_deque.size() == 0)
        return 0;

    long start_time = -1;  // -1 表示未开始计算
    for (size_t i = 0; i < distraction_deque.size(); ++i) {
        long timestamp = distraction_deque[i].first;
        bool is_true = distraction_deque[i].second;

        if (is_true) {
            if (start_time == -1) {
                start_time = timestamp;  // 记录一个新的连续 true 段落的起始时间
            }
        } else {
            if (start_time != -1) {
                // 遇到 false，结束当前连续 true 段落的计算
                distraction_sum_time += distraction_deque[i - 1].first - start_time;
                start_time = -1;  // 重置为未开始
            }
        }
    }

    // 如果最后一个段落是 true，且没有遇到 false
    if (start_time != -1) {
        distraction_sum_time += distraction_deque.back().first - start_time;
    }

    return distraction_sum_time;
}

long DistractionWarn::GetContinueFrontTime(
    const std::deque<std::pair<long, bool>> distraction_deque) {
    if (distraction_deque.size() == 0)
        return 0;

    long distraction_front_continue_time = 0;
    // std::vector<long> time_vector;  //用于记录时间差数组

    std::deque<std::pair<long, bool>> temp_deque2;
    temp_deque2.clear();
    long cost_time2 = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == false) {
            temp_deque2.emplace_back(v);
            if (temp_deque2.size() >= 2) {
                cost_time2 = temp_deque2.back().first - temp_deque2.front().first;
            }
            distraction_front_continue_time = cost_time2;
        } else {
            temp_deque2.clear();
            cost_time2 = 0;
        }
        // time_vector.emplace_back(cost_time2);
    }
    // distraction_front_continue_time = *(std::max_element(
    //     time_vector.begin(), time_vector.end()));  //找出时间窗中最大时间作为结果输出

    return distraction_front_continue_time;
}

void DistractionWarn::Reset() {
    ok_flag = false;

    distraction_short_result.clear();
    distraction_long_result.clear();

    head_yaw_3s_min = -99.0f;
    head_yaw_3s_max = 99.0f;

    alarm_start_time = cache_time;

    histor_warn_type = DISTRACTION_NORMAL;

    // 重置眼睑曲率状态管理变量
    continuous_abnormal_count_ = 0;
    continuous_normal_count_ = 0;
    eye_curve_active_ = false;

    return;
}

void DistractionWarn::ResetNofaceMixdata() {
    facevalid_mix_distra.clear();
}

DistractionType DistractionWarn::GetWarnStatus(int speed) {
    DistractionType status = DISTRACTION_NORMAL;
    //可视化
    distraction_info = {0};
    distraction_info.distraction_continue_time = GetContinueDistractionTime(distraction_3s_result);
    distraction_info.distraction_continue_percent =
        GetContinueDistractionPercent(distraction_3s_result, window_time - 150);
    distraction_info.distraction_front_continue_time = GetContinueFrontTime(distraction_3s_result);
    distraction_info.time_gap = 0;
    if (distraction_3s_result.size() > 0)
        distraction_info.time_gap =
            (distraction_3s_result.back().first - distraction_3s_result.front().first);
    //可视化end

    //提前报分神优化，延迟报警(只对长时分心有效)
    bool is_head_yawbias = false;
    if (head_yaw_3s_min > -90.0f && head_yaw_3s_max < 90.f) {
        if (fabs(head_yaw_3s_min - head_yaw_3s_max) >= head_yaw_bias)
            is_head_yawbias = true;
    }
    if (head_yaw_3s_vec.size() > 2 &&
        (head_yaw_3s_vec.back().first - head_yaw_3s_vec.front().first) >=
            (head_yaw_bias_window_time - 150) &&
        is_head_yawbias && histor_warn_type == DISTRACTION_NORMAL) {
        distraction_reason = distraction_reason + " no_distraction2";
    } else {
        bool speed_50_3s = GetResult(distraction_3s_result, window_time - 150);
        // printf("speed_50_3s:%d\n", speed_50_3s);
        if (speed_50_3s && speed >= 20) {
            status = DISTRACTION_FATIGUE1;
        }
    }

#if 0  //defined(BYD_EQ) //和短时分心判断相关需保留
    std::deque<std::pair<long, bool>> distraction_temp_result;
    distraction_temp_result.clear();
    GetDataQueue(distraction_short_result, distraction_temp_result, 30100);  //获取30s的数据

    distraction_info.distraction_sum_time = GetSumDistractionTime(distraction_temp_result);

    bool short_time_distraction = GetShortTimeResult(distraction_temp_result, 10000);
    if (short_time_distraction && speed >= 20) {
        status = DISTRACTION_FATIGUE2;
    }
#endif

    if (histor_warn_type < status) {
        histor_warn_type = status;      //最新的报警等级，存在历史结果
        alarm_start_time = cache_time;  //升级后,更新报警开始时间
        alarm_ok_start_time = cache_time;
    } else {
        status = histor_warn_type;  //持续报上次结果
        alarm_ok_end_time = cache_time;
    }

    //判断降级（注视正常驾驶区域，持续时长≥1s，则退出分心状态）视为触发降级，即为正常状态
    alarm_end_time = cache_time;

    //只触发降级
    std::deque<std::pair<long, bool>> temp_result;
    GetDataQueue(distraction_3s_result, temp_result, 1000);
    if (GetDegradeResult2(temp_result, 3)) {
        // std::cout << "Degrade distract..." << std::endl;
        Reset();
        status = DISTRACTION_NORMAL;
    }

    if (ok_flag) {  //收到ok,清除报警
        // std::cout << "ok_flag, clear distract..." << std::endl;
        Reset();
        status = DISTRACTION_NORMAL;
    }
    return status;
}

void DistractionWarn::SetOk() {
    ok_flag = true;
}

void DistractionWarn::initCalibrationLogger() {
    auto& logger = CalibrationLogger::getInstance();
    logger.setOutputInterval(CALIBRATION_DEBUG_INTERVAL_MS);
    logger.setEnabled(log_switch);

    // 添加控制台观察者
    auto console_observer = std::make_shared<ConsoleLogObserver>();
    logger.addObserver(console_observer);

    // 重置统计信息
    logger.resetStats();
    logger.setPhase(CalibrationPhase::PREPARING);
}

//自动标定分神
TXDistracCaliStatus DistractionWarn::auto_calibration_distraction(TXDmsFaceInfo face_info,
                                                                  const TXCarInfo* car_info,
                                                                  const float leye_uper_curve_score,
                                                                  const float reye_uper_curve_score,
                                                                  long now_ts) {
    // 获取日志系统实例
    auto& logger = CalibrationLogger::getInstance();

    // 记录车辆信息
    logger.recordVehicleInfo(car_info->speed, car_info->gear);

    // 读取json文件，本地调试模式
    std::string config_file = "sight.json";
    //配置文件存在，则从配置文件中读取
    if (!read_json && access(config_file.c_str(), F_OK) == 0) {
        Json::Reader json_reader2;
        Json::Value root2;
        std::ifstream infile2(config_file, std::ios::binary);
        if (!infile2.is_open()) {
            std::cout << "Open  sight config file failed!" << std::endl;
        }

        if (!json_reader2.parse(infile2, root2)) {
            std::cout << "Parse  sight json config file failed!" << std::endl;
        } else {
            read_json = true;
            log_switch = root2["log_switch"].asBool();

            headpose_pitch_threshold = root2["headpose_pitch_threshold"].asDouble();
            gaze_pitch_threshold = root2["gaze_pitch_threshold"].asDouble();
            headpose_yaw_threshold = root2["headpose_yaw_threshold"].asDouble();
            gaze_yaw_threshold = root2["gaze_yaw_threshold"].asDouble();

            window_time = root2["window_time"].asInt();
            distraction_thr = root2["distraction_thr"].asDouble();

            head_yaw_bias_window_time = root2["head_yaw_bias_window_time"].asInt();

            calibrate_headpose_yaw_min = root2["calibrate_headpose_yaw_min"].asDouble();
            calibrate_headpose_yaw_max = root2["calibrate_headpose_yaw_max"].asDouble();
            calibrate_headpose_pitch_min = root2["calibrate_headpose_pitch_min"].asDouble();
            calibrate_headpose_pitch_max = root2["calibrate_headpose_pitch_max"].asDouble();
            calibrate_headpose_roll_min = root2["calibrate_headpose_roll_min"].asDouble();
            calibrate_headpose_roll_max = root2["calibrate_headpose_roll_max"].asDouble();

            pitch_down = root2["pitch_down"].asDouble();
            pitch_up = root2["pitch_up"].asDouble();
            yaw_left = root2["yaw_left"].asDouble();
            yaw_right = root2["yaw_right"].asDouble();
            roll_left = root2["roll_left"].asDouble();
            roll_right = root2["roll_right"].asDouble();

            steering_wheel_angle_min = root2["steering_wheel_angle_min"].asDouble();
            steering_wheel_angle_max = root2["steering_wheel_angle_max"].asDouble();
            headpose_yaw_normal_min = root2["headpose_yaw_normal_min"].asDouble();
            headpose_yaw_normal_max = root2["headpose_yaw_normal_max"].asDouble();
            headpose_pitch_normal_min = root2["headpose_pitch_normal_min"].asDouble();
            headpose_pitch_normal_max = root2["headpose_pitch_normal_max"].asDouble();
            headpose_roll_normal_min = root2["headpose_roll_normal_min"].asDouble();
            headpose_roll_normal_max = root2["headpose_roll_normal_max"].asDouble();
            headpose_yaw_left = root2["head_pose_yaw_left_offset"].asDouble();
            headpose_yaw_right = root2["head_pose_yaw_right_offset"].asDouble();
            headpose_pitch_up = root2["head_pose_pitch_up_offset"].asDouble();
            headpose_pitch_down = root2["head_pose_pitch_down_offset"].asDouble();
            headpose_spe_glasses_yaw_left =
                root2["head_pose_spe_glasses_yaw_left_offset"].asDouble();
            headpose_spe_glasses_yaw_right =
                root2["head_pose_spe_glasses_yaw_right_offset"].asDouble();
            headpose_spe_glasses_pitch_up =
                root2["head_pose_spe_glasses_pitch_up_offset"].asDouble();
            headpose_spe_glasses_pitch_down =
                root2["head_pose_spe_glasses_pitch_down_offset"].asDouble();

            headgaze_yaw_l_offset = root2["headgaze_yaw_l_offset"].asDouble();
            headgaze_yaw_r_offset = root2["headgaze_yaw_r_offset"].asDouble();

            righteye_up_down_proportion = root2["righteye_up_down_proportion"].asDouble();
            lefteye_up_down_proportion = root2["lefteye_up_down_proportion"].asDouble();

            head_yaw_bias = root2["head_yaw_bias"].asDouble();

            if (root2.isMember("fusion_use_eye")) {
                fusion_use_eye = root2["fusion_use_eye"].asInt();
            }

            if (root2.isMember("region_mapping_width")) {
                region_mapping_width = root2["region_mapping_width"].asInt();
            }
            if (root2.isMember("region_mapping_height")) {
                region_mapping_height = root2["region_mapping_height"].asInt();
            }
            if (root2.isMember("tolerate_percentage")) {
                tolerate_percentage = root2["tolerate_percentage"].asFloat();
            }
            if (root2.isMember("region_hull")) {
                cc_assert(root2["region_hull"].isArray());

                std::vector<cv::Point2f> hull;
                for (int k = 0; k < root2["region_hull"].size(); k++) {
                    hull.clear();
                    for (int i = 0; i < root2["region_hull"][k].size(); i++) {
                        hull.clear();
                        for (int j = 0; j < root2["region_hull"][k][i].size(); j += 2) {
                            cc_assert(root2["region_hull"][k][i].size() % 2 == 0);

                            cv::Point2f point;
                            point.x = root2["region_hull"][k][i][j].asFloat();
                            point.y = root2["region_hull"][k][i][j + 1].asFloat();
                            hull.push_back(point);
                        }
                        std::cout << "hull.size():" << hull.size() << std::endl;
                        region_hulls.push_back(hull);
                    }
                    std::cout << "use outside config json,region_hulls.size():"
                              << region_hulls.size() << std::endl;
                }
            }
        }
    }
    // //test
    // log_switch = true;
    if (log_switch) {
        printf("read_json:%d,headpose_yaw_left:%f,headpose_yaw_right:%f,headpose_pitch_up:%f,"
               "headpose_pitch_down:%f \n",
               read_json, headpose_yaw_left, headpose_yaw_right, headpose_pitch_up,
               headpose_pitch_down);
    }

    // 记录标定进度信息
    int head_buffer_size = 0, left_eye_buffer_size = 0, right_eye_buffer_size = 0;
    if (calibrator) {
        head_buffer_size = calibrator->getHeadBufferSize();
        left_eye_buffer_size = calibrator->getLeftEyeBufferSize();
        right_eye_buffer_size = calibrator->getRightEyeBufferSize();
    }

    // 更新标定阶段
    if (auto_calibration) {
        logger.setPhase(CalibrationPhase::COMPLETED);
    } else {
        logger.setPhase(CalibrationPhase::IN_PROGRESS);
    }

    // 记录进度信息（这会触发汇总输出检查）
    logger.recordProgress(head_buffer_size, left_eye_buffer_size, right_eye_buffer_size);
    if (now_ts - last_ts >= 1000) {
        logger.flushStats();
        last_ts = now_ts;
    }
    //自动标定分神逻辑
    if (car_info->speed >= 20 && car_info->gear == TXGearPition::FORWARD) {
        bool is_mask = (face_info.isMask == 1) ?  true : false;
        if (!auto_calibration) {
            //判断人脸角度是否合理，不合理则不进行标定
            if (read_json) {
                if (calibrate_headpose_yaw_min > face_info.head_yaw ||
                    calibrate_headpose_yaw_max < face_info.head_yaw ||
                    calibrate_headpose_pitch_min > face_info.head_pitch ||
                    calibrate_headpose_pitch_max < face_info.head_pitch ||
                    calibrate_headpose_roll_min > face_info.head_roll ||
                    calibrate_headpose_roll_max < face_info.head_roll) {

                    // 记录人脸角度过滤原因
                    std::string angle_type;
                    float angle_value;
                    if (calibrate_headpose_yaw_min > face_info.head_yaw || calibrate_headpose_yaw_max < face_info.head_yaw) {
                        angle_type = "yaw";
                        angle_value = face_info.head_yaw;
                    } else if (calibrate_headpose_pitch_min > face_info.head_pitch || calibrate_headpose_pitch_max < face_info.head_pitch) {
                        angle_type = "pitch";
                        angle_value = face_info.head_pitch;
                    } else {
                        angle_type = "roll";
                        angle_value = face_info.head_roll;
                    }
                    logger.recordFilter("人脸角度超范围", angle_type + "角度" + std::to_string(angle_value) + "超出配置范围");

                    if (log_switch) {
                        printf("DistractionWarn  "
                               "不满足人脸角度正常范围，不可进行自动标定...........\n");
                    }

                    return CALIBRATE_CONDITION_UNDONE;
                }

                if (car_info->steer_whl_snsr_rad < steering_wheel_angle_min ||
                    car_info->steer_whl_snsr_rad > steering_wheel_angle_max) {

                    // 记录方向盘角度过滤原因
                    logger.recordFilter("方向盘角度无效", "方向盘转角" + std::to_string(car_info->steer_whl_snsr_rad) + "超出配置范围");

                    if (log_switch) {
                        printf("DistractionWarn  "
                               "不满足方向盘转角正常范围，不可进行自动标定...........\n");
                    }

                    return CALIBRATE_CONDITION_UNDONE;
                }
            } else {
                headpose_pitch_threshold = HEADPOSE_PITCH_THR;
                gaze_pitch_threshold = GAZE_PITCH_THR;
                headpose_yaw_threshold = HEADPOSE_YAW_THR;
                gaze_yaw_threshold = GAZE_YAW_THR;

                headgaze_yaw_l_offset = HEAD_YAW_L_OFFSET;
                headgaze_yaw_r_offset = HEAD_YAW_R_OFFSET;

                righteye_up_down_proportion = RIGHTEYE_UP_DOWN_PROPORTION;
                lefteye_up_down_proportion = LEFTEYE_UP_DOWN_PROPORTION;

                head_yaw_bias = HEAD_YAW_BIAS;
                head_yaw_bias_window_time = HEAD_YAW_BIAS_WINDOW_TIME;

                window_time = WINDOW_TIME;
                distraction_thr = DISTRACTION_THR;

                fusion_use_eye = FUSION_USE_EYE;
                region_mapping_width = REGION_MAPPING_WIDTH;
                region_mapping_height = REGION_MAPPING_HEIGHT;
                tolerate_percentage = TOLERATE_PERCENTAGE;
                region_hulls = REGION_HULLS;

                if (CALIBRATE_HEADPOSE_YAW_NORMAL_MIN - ((is_mask) ? HEAD_POSE_SPE_GLASSES_YAW_L : 0) > face_info.head_yaw ||
                    CALIBRATE_HEADPOSE_YAW_NORMAL_MAX + ((is_mask) ? HEAD_POSE_SPE_GLASSES_YAW_R : 0) < face_info.head_yaw ||
                    CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN - ((is_mask) ? HEAD_POSE_SPE_GLASSES_PITCH_D : 0) > face_info.head_pitch ||
                    CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX + ((is_mask) ? HEAD_POSE_SPE_GLASSES_PITCH_U : 0) < face_info.head_pitch ||
                    CALIBRATE_HEADPOSE_ROLL_NORMAL_MIN > face_info.head_roll ||
                    CALIBRATE_HEADPOSE_ROLL_NORMAL_MAX < face_info.head_roll) {

                    // 记录人脸角度过滤原因(默认配置)
                    float yaw_min = CALIBRATE_HEADPOSE_YAW_NORMAL_MIN - ((is_mask) ? HEAD_POSE_SPE_GLASSES_YAW_L : 0);
                    float yaw_max = CALIBRATE_HEADPOSE_YAW_NORMAL_MAX + ((is_mask) ? HEAD_POSE_SPE_GLASSES_YAW_R : 0);
                    float pitch_min = CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN - ((is_mask) ? HEAD_POSE_SPE_GLASSES_PITCH_D : 0);
                    float pitch_max = CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX + ((is_mask) ? HEAD_POSE_SPE_GLASSES_PITCH_U : 0);

                    std::string angle_type;
                    float angle_value;
                    if (yaw_min > face_info.head_yaw || yaw_max < face_info.head_yaw) {
                        angle_type = "yaw";
                        angle_value = face_info.head_yaw;
                    } else if (pitch_min > face_info.head_pitch || pitch_max < face_info.head_pitch) {
                        angle_type = "pitch";
                        angle_value = face_info.head_pitch;
                    } else {
                        angle_type = "roll";
                        angle_value = face_info.head_roll;
                    }
                    logger.recordFilter("人脸角度超范围(默认配置)", angle_type + "角度" + std::to_string(angle_value) + "超出默认范围");

                    if (log_switch) {
                        printf("DistractionWarn "
                               "不满足人脸角度正常范围，不可进行自动标定2...........\n");
                    }
                    return CALIBRATE_CONDITION_UNDONE;
                }
                if (car_info->steer_whl_snsr_rad < STEERING_WHEEL_ANGLE_MIN ||
                    car_info->steer_whl_snsr_rad > STEERING_WHEEL_ANGLE_MAX) {

                    // 记录方向盘角度过滤原因(默认配置)
                    logger.recordFilter("方向盘角度无效(默认配置)", "方向盘转角" + std::to_string(car_info->steer_whl_snsr_rad) + "超出默认范围");

                    if (log_switch) {
                        printf("DistractionWarn  "
                               "不满足方向盘转角正常范围，不可进行自动标定2...........\n");
                    }

                    return CALIBRATE_CONDITION_UNDONE;
                }
            }
        }

        // 数据通过所有过滤条件，进入标定流程

        StartCalibration(is_mask, face_info.head_yaw, face_info.head_pitch, face_info.head_roll,
                         face_info.left_eye_landmark.yaw, face_info.left_eye_landmark.pitch,
                         face_info.right_eye_landmark.yaw, face_info.right_eye_landmark.pitch,
                         face_info.left_eye_landmark.pupil_score,
                         face_info.right_eye_landmark.pupil_score, leye_uper_curve_score,
                         reye_uper_curve_score);

        headpose_yaw_ = temp_headpose_yaw_mean;
        headpose_pitch_ = temp_headpose_pitch_mean;
        headpose_roll_ = temp_headpose_roll_mean;

        gaze_left_eye_pitch_ = temp_lefteye_gaze_pitch_mean;
        gaze_left_eye_yaw_ = temp_lefteye_gaze_yaw_mean;
        gaze_right_eye_pitch_ = temp_righteye_gaze_pitch_mean;
        gaze_right_eye_yaw_ = temp_righteye_gaze_yaw_mean;

        if (log_switch) {
            printf("DistractionWarn finally "
                   "headpose_yaw_:%f,headpose_pitch_:%f,headpose_roll_:%f,gaze_right_eye_"
                   "pitch_:%f,gaze_right_"
                   "eye_yaw_:%f,gaze_left_eye_yaw_:%f, gaze_left_eye_pitch_:%f\n",
                   headpose_yaw_, headpose_pitch_, headpose_roll_, gaze_right_eye_pitch_,
                   gaze_right_eye_yaw_, gaze_left_eye_yaw_, gaze_left_eye_pitch_);
        }

        bool eye_cali_finish = false;
        if (fusion_use_eye == 1) {
            eye_cali_finish = cali_status.reye_cali_finish;
        } else if (fusion_use_eye == 0) {
            eye_cali_finish = cali_status.leye_cali_finish;
        } else if (fusion_use_eye == 2) {
            eye_cali_finish = cali_status.reye_cali_finish || cali_status.leye_cali_finish;
        }

        if (cali_status.head_cali_finish && eye_cali_finish) {
            auto_calibration = true;
            // 记录标定成功
            logger.recordSuccess(cali_status.head_cali_finish, cali_status.leye_cali_finish, cali_status.reye_cali_finish);
        } else {
            // 记录标定未完成
            std::string required_parts = (fusion_use_eye == 0 ? "头部+左眼" : (fusion_use_eye == 1 ? "头部+右眼" : "头部+任一眼"));
            logger.recordFailure("标定未完成，需要: " + required_parts);
        }

        // 标定进度信息已在上面的时间间隔输出中显示

        if (!auto_calibration) {
            if (log_switch) {
                printf("DistractionWarn  自动标定中...........\n");
            }
            return CALIBRATE_DOING;
        }
        return CALIBRATE_DONE;
    } else if (auto_calibration) {
        if (log_switch) {
            printf(
                "DistractionWarn  "
                "自动标定已完成...........headpose_yaw_:%f,headpose_pitch_:%f,gaze_right_eye_pitch_"
                ":%f,gaze_right_eye_yaw_:%f ,gaze_left_eye_yaw_:%f,gaze_left_eye_pitch_:%f\n",
                headpose_yaw_, headpose_pitch_, gaze_right_eye_pitch_, gaze_right_eye_yaw_,
                gaze_left_eye_yaw_, gaze_left_eye_pitch_);
        }
        return CALIBRATE_DONE;
    } else {
        //防止中途标定一半(车速不满足，档位不满足等)，重新清理数据//累计时间窗逻辑则不需要重新清理
        // ClearCalibration();

        // 记录过滤原因
        if (car_info->speed < 20) {
            logger.recordFilter("车速过低", "车速" + std::to_string(car_info->speed) + "km/h低于20km/h");
        } else if (car_info->gear != TXGearPition::FORWARD) {
            logger.recordFilter("档位无效", "档位" + std::to_string(car_info->gear) + "不是前进档");
        }

        if (log_switch) {
            if (car_info->speed < 20)
                printf("DistractionWarn  车速不满足，不进行分神标定,使用默认参数...........\n");
            if (car_info->gear != TXGearPition::FORWARD)
                printf("DistractionWarn  档位不是前进挡，不进行分神标定,使用默认参数...........\n");
        }

        return CALIBRATE_CONDITION_UNDONE;
    }

    // 日志系统会自动管理时间间隔
}

void DistractionWarn::updateHeadYawBias(const TXDmsFaceInfo& face_info, long now_ts) {
    constexpr float kMaxHeadAngle = 50.0f;

    distract_param.head_yaw_bias = 0;
    bool is_mask = (face_info.isMask == 1) ? true : false;
    if (std::abs(face_info.head_pitch) < kMaxHeadAngle &&
        std::abs(face_info.head_roll) < kMaxHeadAngle && !is_mask) {
        float clamped_yaw = std::max(-50.0f, std::min(face_info.head_yaw, 50.0f));
        head_yaw_3s_vec.emplace_back(std::make_pair(now_ts, clamped_yaw));

        // 平滑head yaw bias抑制逻辑的数据统计，减少特殊场景对报警的影响
        // 此自定义滤波算法要求至少有6个数据点, 以确保用于计算的索引不会重叠
        constexpr int kMinSamplesForYawBias = 6;
        if (head_yaw_3s_vec.size() >= kMinSamplesForYawBias) {
            while ((head_yaw_3s_vec.back().first - head_yaw_3s_vec.front().first) >
                   head_yaw_bias_window_time) {
                head_yaw_3s_vec.pop_front();
            }
            std::vector<float> yaw_values;
            yaw_values.reserve(head_yaw_3s_vec.size());
            for (const auto& p : head_yaw_3s_vec) {
                yaw_values.push_back(p.second);
            }
            std::sort(yaw_values.begin(), yaw_values.end());

            // 为提高鲁棒性，忽略两端的极值,
            // 然后分别平均它们旁边的两个值，以获得对头部转动范围的稳定估计
            float min2 = yaw_values[1];
            float min3 = yaw_values[2];

            float max2 = yaw_values[yaw_values.size() - 2];
            float max3 = yaw_values[yaw_values.size() - 3];
            head_yaw_3s_min = (min2 + min3) / 2.0f;
            head_yaw_3s_max = (max2 + max3) / 2.0f;
            distract_param.head_yaw_bias = fabs(head_yaw_3s_min - head_yaw_3s_max);
        } 
    }
}

bool DistractionWarn::isSteeringAngleInvalid(const TXCarInfo* car_info,
                                             std::string& distraction_reason) {
    // 角度 = 弧度 × (180/π)
    float angle = car_info->steer_whl_snsr_rad * (180 / 3.14159);
    float max_value = std::max(std::fabs(30.0f - (0.375f * car_info->speed)), 10.0f);
    // printf("angle:%f,max_value:%f\n", angle, max_value);
    if (car_info->turn_light == TURN_ON_LEFT || car_info->turn_light == TURN_ON_RIGHT ||
        car_info->speed < 20 || fabs(angle) >= max_value) {
        // printf("Being distracted is suppressed! \n", angle, max_value);
        distraction_reason = "no1";
        return true;
    }
    return false;
}

bool DistractionWarn::isSpeGlassesSituation(const TXDmsFaceInfo& face_info) {
    bool is_spe_glasses_solution = false;
#if defined(BYD_HA6)
    bool is_glasses = false;
    bool left_gaze_visuable = false;
    bool right_gaze_visuable = false;
    if (face_info.right_eye_landmark.eye_score != 0) {  //因为有-1的值，所以得区分-1的情况
        right_gaze_visuable = true;
    } else if (face_info.left_eye_landmark.eye_score != 0) {
        left_gaze_visuable = true;
    }
    if (face_info.isGlass != 0)
        is_glasses = true;
    // 因为眼睛状态的检测不稳定，所以当一定时间内有检测到眼镜，则认为是戴眼镜状态
    glasses_vec.emplace_back(is_glasses);
    if (glasses_vec.size() >= 2) {
        for (auto glasses_status : glasses_vec) {
            if (glasses_status == true) {
                is_glasses = true;
                break;
            }
        }
        if (glasses_vec.size() >= 100) {
            glasses_vec.pop_front();
        }
    }

    if (is_glasses) {
        if (fusion_use_eye == 0) {
            is_spe_glasses_solution = !left_gaze_visuable;
        } else if (fusion_use_eye == 1) {
            is_spe_glasses_solution = !right_gaze_visuable;
        } else if (fusion_use_eye == 2) {
            is_spe_glasses_solution = (!left_gaze_visuable && !right_gaze_visuable);
        }
    }
#endif
    return is_spe_glasses_solution;
}

bool DistractionWarn::isSpeMaskSituation(const TXDmsFaceInfo& face_info) {
    bool is_spe_mask_solution = false;
    bool is_mask = false;

    if (face_info.isMask == 1)
        is_mask = true;
    // 因为眼睛状态的检测不稳定，所以当一定时间内有检测到眼镜，则认为是戴眼镜状态
    mask_vec.emplace_back(is_mask);
    if (mask_vec.size() >= 2) {
        for (auto mask_status : mask_vec) {
            if (mask_status == true) {
                is_mask = true;
                break;
            }
        }
        if (mask_vec.size() >= 30) {
            mask_vec.pop_front();
        }
    }

    if (is_mask) {
        is_spe_mask_solution = true;
    }
    return is_spe_mask_solution;
}

void DistractionWarn::calcOffsetFromSteeringAngle(const TXCarInfo* car_info,
                                                  bool auto_calibration,
                                                  float& headpose_yaw_l_offset,
                                                  float& headpose_yaw_r_offset) {
    //车辆小幅转弯逻辑

    float angle = car_info->steer_whl_snsr_rad * (180 / 3.14159);
    if (angle >= -10 && angle <= 10 && auto_calibration) {
        float unit = 7.0f / 10;  //每一度0.3
        float adjust_value;
        if (angle < 0) {
            adjust_value = floor(angle);  //向下取整

        } else {
            adjust_value = ceil(angle);  //向上取整
        }
        adjust_value = adjust_value * unit;
        // printf("adjust_value:%f\n", adjust_value);

        if (adjust_value < 0) {
            headpose_yaw_l_offset = fabs(adjust_value);
            // gaze_yaw_l_offset = fabs(adjust_value);

        } else {
            headpose_yaw_r_offset = adjust_value;
            // gaze_yaw_r_offset = adjust_value;
        }
    }
}

DistractionWarn::HeadRotationRange DistractionWarn::calcNonDistractHeadRotation(
    const TXCarInfo* car_info, const TXDmsFaceInfo& face_info) {
    HeadRotationRange head_rotation_range = {0};

    // 针对眼镜且眼睛不可见场景下做的抑制头部误报逻辑
    bool is_spe_glasses_solution = isSpeGlassesSituation(face_info);
    // 针对戴口罩场景下做的抑制头部误报逻辑
    bool is_spe_mask_solution = isSpeMaskSituation(face_info);

    if (has_head_cali) {
        float headpose_yaw_l_offset = 0.0f;
        float headpose_yaw_r_offset = 0.0f;
        // 计算方向盘角度对头部非分心范围的偏移值
        calcOffsetFromSteeringAngle(car_info, auto_calibration, headpose_yaw_l_offset,
                                    headpose_yaw_r_offset);
        float head_yaw_min = 0.0f, head_yaw_max = 0.0f, head_pitch_min = 0.0f,
              head_pitch_max = 0.0f;

        if (read_json) {
            head_yaw_min = headpose_yaw_ - headpose_yaw_left - headpose_yaw_l_offset;
            head_yaw_max = headpose_yaw_ + headpose_yaw_right + headpose_yaw_r_offset;
            head_pitch_min = headpose_pitch_ - headpose_pitch_down;
            head_pitch_max = headpose_pitch_ + headpose_pitch_up;
            if (is_spe_glasses_solution) {
                head_yaw_min = head_yaw_min;  // - headpose_spe_glasses_yaw_left;
                head_yaw_max = head_yaw_max;  // + headpose_spe_glasses_yaw_right;
                head_pitch_min = head_pitch_min - headpose_spe_glasses_pitch_down;
                head_pitch_max = head_pitch_max + headpose_spe_glasses_pitch_up;
            }
            if (is_spe_mask_solution) {
                head_yaw_min = head_yaw_min - headpose_spe_glasses_yaw_left;
                head_yaw_max = head_yaw_max + headpose_spe_glasses_yaw_right;
                head_pitch_min = head_pitch_min - headpose_spe_glasses_pitch_down;
                head_pitch_max = head_pitch_max + headpose_spe_glasses_pitch_up;
            }
        } else {
            head_yaw_min = headpose_yaw_ - HEAD_POSE_YAW_L - headpose_yaw_l_offset;
            head_yaw_max = headpose_yaw_ + HEAD_POSE_YAW_R + headpose_yaw_r_offset;
            head_pitch_min = headpose_pitch_ - HEAD_POSE_PITCH_D;
            head_pitch_max = headpose_pitch_ + HEAD_POSE_PITCH_U;
            if (is_spe_glasses_solution) {
                head_yaw_min = head_yaw_min - HEAD_POSE_SPE_GLASSES_YAW_L;
                head_yaw_max = head_yaw_max + HEAD_POSE_SPE_GLASSES_YAW_R;
                head_pitch_min = head_pitch_min - HEAD_POSE_SPE_GLASSES_PITCH_D;
                head_pitch_max = head_pitch_max + HEAD_POSE_SPE_GLASSES_PITCH_U;
            }
            if (is_spe_mask_solution) {
                head_yaw_min = head_yaw_min - HEAD_POSE_SPE_GLASSES_YAW_L;
                head_yaw_max = head_yaw_max + HEAD_POSE_SPE_GLASSES_YAW_R;
                head_pitch_min = head_pitch_min - HEAD_POSE_SPE_GLASSES_PITCH_D;
                head_pitch_max = head_pitch_max + HEAD_POSE_SPE_GLASSES_PITCH_U;
            }
        }
        distract_param.head_yaw_min = head_rotation_range.minyaw = head_yaw_min;
        distract_param.head_yaw_max = head_rotation_range.maxyaw = head_yaw_max;
        distract_param.head_pitch_min = head_rotation_range.minpitch = head_pitch_min;
        distract_param.head_pitch_max = head_rotation_range.maxpitch = head_pitch_max;
    } else {
        // std::cout << "---------2   is_spe_mask_solution:" << is_spe_mask_solution <<
        //  " " << head_rotation_range.minyaw << " " << head_rotation_range.maxyaw << " " 
        //  << head_rotation_range.minpitch << " " << head_rotation_range.maxpitch << std::endl;
        if (read_json) {
            // printf("\033[33m --hjh-- file:dms_distraction_warning_byd.cpp line:%d info:hello \033[0m \n ",__LINE__);
            head_rotation_range.minyaw = headpose_yaw_normal_min;
            head_rotation_range.maxyaw = headpose_yaw_normal_max;
            head_rotation_range.minpitch = headpose_pitch_normal_min;
            head_rotation_range.maxpitch = headpose_pitch_normal_max;
            head_rotation_range.minroll = headpose_roll_normal_min;
            head_rotation_range.maxroll = headpose_roll_normal_max;
            // if (is_spe_glasses_solution) {
            //     head_rotation_range.minyaw = headpose_yaw_normal_min;
            //     head_rotation_range.maxyaw = headpose_yaw_normal_max;
            //     head_rotation_range.minpitch = headpose_pitch_normal_min - headpose_spe_glasses_pitch_down;
            //     head_rotation_range.maxpitch = headpose_pitch_normal_max + headpose_spe_glasses_pitch_up;
            // }
            if (is_spe_mask_solution) {
                head_rotation_range.minyaw = headpose_yaw_normal_min - headpose_spe_glasses_yaw_left;
                head_rotation_range.maxyaw = headpose_yaw_normal_max + headpose_spe_glasses_yaw_right;
                head_rotation_range.minpitch = headpose_pitch_normal_min - headpose_spe_glasses_pitch_down;
                head_rotation_range.maxpitch = headpose_pitch_normal_max + headpose_spe_glasses_pitch_up;
            }
        } else {
            // printf("\033[33m --hjh-- file:dms_distraction_warning_byd.cpp line:%d info:hello \033[0m \n ",__LINE__);
            head_rotation_range.minyaw = HEADPOSE_YAW_NORMAL_MIN;
            head_rotation_range.maxyaw = HEADPOSE_YAW_NORMAL_MAX;
            head_rotation_range.minpitch = HEADPOSE_PITCH_NORMAL_MIN;
            head_rotation_range.maxpitch = HEADPOSE_PITCH_NORMAL_MAX;
            head_rotation_range.minroll = HEADPOSE_ROLL_NORMAL_MIN;
            head_rotation_range.maxroll = HEADPOSE_ROLL_NORMAL_MAX;
            // if (is_spe_glasses_solution) {
            //     head_rotation_range.minyaw = head_rotation_range.minyaw;
            //     head_rotation_range.maxyaw = head_rotation_range.maxyaw;
            //     head_rotation_range.minpitch = head_rotation_range.minpitch - HEAD_POSE_SPE_GLASSES_PITCH_D;
            //     head_rotation_range.maxpitch = head_rotation_range.maxpitch + HEAD_POSE_SPE_GLASSES_PITCH_U;
            // }
            if (is_spe_mask_solution) {
                head_rotation_range.minyaw = head_rotation_range.minyaw - HEAD_POSE_SPE_GLASSES_YAW_L;
                head_rotation_range.maxyaw = head_rotation_range.maxyaw + HEAD_POSE_SPE_GLASSES_YAW_R;
                head_rotation_range.minpitch = head_rotation_range.minpitch - HEAD_POSE_SPE_GLASSES_PITCH_D;
                head_rotation_range.maxpitch = head_rotation_range.maxpitch + HEAD_POSE_SPE_GLASSES_PITCH_U;
            }
        }
        // std::cout << "---------1   is_spe_mask_solution:" << is_spe_mask_solution <<
        //  " " << head_rotation_range.minyaw << " " << head_rotation_range.maxyaw << " " 
        //  << head_rotation_range.minpitch << " " << head_rotation_range.maxpitch << std::endl;
    }

    return head_rotation_range;
}

bool DistractionWarn::checkFalseDistraction(TXDmsFaceInfo& face_info,
                                            const HeadRotationRange& head_rotation_range,
                                            std::string& distraction_reason) {
    //过滤仰头+张嘴误报分神逻辑
    constexpr float MOUTH_OPENING_THRESHOLD = 0.5f;
    bool is_false_distraction = false;
    if (auto_calibration && (head_rotation_range.maxpitch < face_info.head_pitch) &&
        (face_info.mouth_opening > MOUTH_OPENING_THRESHOLD)) {
        distraction_reason = "no3";
        is_false_distraction = true;
    }

    return is_false_distraction;
}

bool DistractionWarn::checkHeadPoseDistraction(const TXDmsFaceInfo& face_info,
                                               const HeadRotationRange& head_rotation_range,
                                               std::string& detailed_reason) {
    bool head_pose = false;
    detailed_reason = "";

    if (auto_calibration) {
        if (head_rotation_range.minyaw != 0 && head_rotation_range.maxyaw != 0 &&
            head_rotation_range.minpitch != 0 && head_rotation_range.maxpitch != 0) {
            // 计算椭圆参数
            headrange_center_yaw = (head_rotation_range.minyaw + head_rotation_range.maxyaw) / 2.0f;
            headrange_center_pitch =
                (head_rotation_range.minpitch + head_rotation_range.maxpitch) / 2.0f;
            headrange_a = (head_rotation_range.maxyaw - head_rotation_range.minyaw) / 2.0f;
            headrange_b = (head_rotation_range.maxpitch - head_rotation_range.minpitch) / 2.0f;
            // 椭圆方程检测
            float normalized_yaw = (face_info.head_yaw - headrange_center_yaw) / headrange_a;
            float normalized_pitch = (face_info.head_pitch - headrange_center_pitch) / headrange_b;
            float ellipse_value =
                normalized_yaw * normalized_yaw + normalized_pitch * normalized_pitch;
            // std::cout << " ellipse_value:" << ellipse_value << "center_yaw:" <<
            //     headrange_center_yaw << " center_pitch:" << headrange_center_pitch << " a:" << headrange_a << " b:" << headrange_b << std::endl;

            if (ellipse_value > 1.0f) {
                head_pose = true;
                // 判断具体超出方向
                if (face_info.head_yaw < head_rotation_range.minyaw) {
                    detailed_reason = "headyaw minlimit";
                } else if (face_info.head_yaw > head_rotation_range.maxyaw) {
                    detailed_reason = "headyaw maxlimit";
                } else if (face_info.head_pitch < head_rotation_range.minpitch) {
                    detailed_reason = "headpitch minlimit";
                } else if (face_info.head_pitch > head_rotation_range.maxpitch) {
                    detailed_reason = "headpitch maxlimit";
                } else {
                    // 在四个角落区域
                    detailed_reason = "headpose corner area";
                }
            }
        }
    } else {
        if (face_info.head_yaw < (head_rotation_range.minyaw) ||
            face_info.head_yaw > (head_rotation_range.maxyaw)) {
            head_pose = true;
        } else if ((face_info.head_pitch < (head_rotation_range.minpitch) ||
                    face_info.head_pitch > (head_rotation_range.maxpitch)) /*&&
                   (face_info.head_roll < (head_rotation_range.minroll) ||
                    face_info.head_roll > (head_rotation_range.maxroll))*/) {
            head_pose = true;
        }
    }
    return head_pose;
}

bool DistractionWarn::isFaceValid(const TXDmsFaceInfo& face_info) {
    bool is_face_valid = false;

    if (face_info.score >= kMinFaceScore)
        is_face_valid = true;

    return is_face_valid;
}

inline bool isGazeCredible(const TXEyeLandmark& eye_landmark) {
    return (eye_landmark.pupil_score > 0 && eye_landmark.eye_score > 0);
}
inline bool isModelDetEye(const TXEyeLandmark& eye_landmark) {
    return (eye_landmark.pupil_score != 0 && eye_landmark.eye_score != 0);
}
std::pair<float, float> DistractionWarn::calcFusionRotationAngle(const bool is_mask, const TXEyeLandmark& eye_landmark,
                                                                 float cur_head_yaw,
                                                                 float cur_head_pitch) {
    float fusion_yaw = 0.0f, fusion_pitch = 0.0f;
    if (isGazeCredible(eye_landmark)) {
        fusion_yaw = eye_landmark.yaw * gaze_yaw_threshold + (is_mask ? 0 : cur_head_yaw * headpose_yaw_threshold);
        // 约束head pitch对最终视线点位的影响，head pitch对视线的影响被约束在[-2,0]
        float head_pitch_diff = cur_head_pitch - headpose_pitch_;
        const float pitch_diff_bfheadpose = -2.0f;
        if (head_pitch_diff > 0) {
            cur_head_pitch = headpose_pitch_;
        } else if ((cur_head_pitch - headpose_pitch_) * headpose_pitch_threshold < pitch_diff_bfheadpose) {
                cur_head_pitch = pitch_diff_bfheadpose / headpose_pitch_threshold + headpose_pitch_;
        }
        fusion_pitch =
            eye_landmark.pitch * gaze_pitch_threshold + (is_mask ? 0 : cur_head_pitch * headpose_pitch_threshold);

        distract_param.current_right_gaze_vf_yaw = fusion_yaw;
        distract_param.current_right_gaze_vf_pitch = fusion_pitch;
    }
    return std::make_pair(fusion_yaw, fusion_pitch);
}

DistractionWarn::GazeDistractedType DistractionWarn::calcPreMapping(const TXDmsFaceInfo& face_info,
                                                                    float& fusion_yaw,
                                                                    float& fusion_pitch,
                                                                    float& cali_yaw,
                                                                    float& cali_pitch) {
    GazeDistractedType eye_gaze_type = GAZE_DISTRACTED_TYPE_UNDISTRACTED;
    fusion_yaw = 0.0f;
    fusion_pitch = 0.0f;
    cali_yaw = 0.0f;
    cali_pitch = 0.0f;

    if (auto_calibration) {
        bool is_leye_model_det = isModelDetEye(face_info.left_eye_landmark);
        bool is_reye_model_det = isModelDetEye(face_info.right_eye_landmark);
        GazeDistractedType leye_gaze_type =
            is_leye_model_det ? GAZE_DISTRACTED_TYPE_NONE : GAZE_DISTRACTED_TYPE_MODELNOTDET;
        GazeDistractedType reye_gaze_type =
            is_reye_model_det ? GAZE_DISTRACTED_TYPE_NONE : GAZE_DISTRACTED_TYPE_MODELNOTDET;

        if (leye_gaze_type == GAZE_DISTRACTED_TYPE_NONE) {
            bool is_leye_gaze_credible = isGazeCredible(face_info.left_eye_landmark);
            if (!is_leye_gaze_credible) {
                leye_gaze_type = GAZE_DISTRACTED_TYPE_INCREDIBLE;
            }
        }
        if (reye_gaze_type == GAZE_DISTRACTED_TYPE_NONE) {
            bool is_reye_gaze_credible = isGazeCredible(face_info.right_eye_landmark);
            if (!is_reye_gaze_credible) {
                reye_gaze_type = GAZE_DISTRACTED_TYPE_INCREDIBLE;
            }
        }
        bool is_mask = (face_info.isMask == 1) ? true : false;
        auto [leye_fusion_yaw, leye_fusion_pitch] = calcFusionRotationAngle(is_mask,
            face_info.left_eye_landmark, face_info.head_yaw, face_info.head_pitch);
        auto [reye_fusion_yaw, reye_fusion_pitch] = calcFusionRotationAngle(is_mask,
            face_info.right_eye_landmark, face_info.head_yaw, face_info.head_pitch);

        switch (fusion_use_eye) {
            case FUSE_EYE_LEFT:
                eye_gaze_type = leye_gaze_type;
                cali_yaw = temp_lefteye_gaze_yaw_mean;
                cali_pitch = temp_lefteye_gaze_pitch_mean;
                fusion_yaw = leye_fusion_yaw;
                fusion_pitch = leye_fusion_pitch;
                break;
            case FUSE_EYE_RIGHT:
                eye_gaze_type = reye_gaze_type;
                cali_yaw = temp_righteye_gaze_yaw_mean;
                cali_pitch = temp_righteye_gaze_pitch_mean;
                fusion_yaw = reye_fusion_yaw;
                fusion_pitch = reye_fusion_pitch;
                break;
            case FUSE_EYE_BOTH:
                eye_gaze_type = std::min(leye_gaze_type, reye_gaze_type);
                if ((leye_gaze_type == GAZE_DISTRACTED_TYPE_NONE && has_leye_cali) &&
                    (reye_gaze_type == GAZE_DISTRACTED_TYPE_NONE && has_reye_cali)) {
                    cali_pitch =
                        (temp_righteye_gaze_pitch_mean + temp_lefteye_gaze_pitch_mean) / 2.0f;
                    cali_yaw = (temp_righteye_gaze_yaw_mean + temp_lefteye_gaze_yaw_mean) / 2.0f;
                    fusion_pitch = (reye_fusion_pitch + leye_fusion_pitch) / 2.0f;
                    fusion_yaw = (reye_fusion_yaw + leye_fusion_yaw) / 2.0f;
                } else if (leye_gaze_type == GAZE_DISTRACTED_TYPE_NONE && has_leye_cali) {
                    cali_pitch = temp_lefteye_gaze_pitch_mean;
                    cali_yaw = temp_lefteye_gaze_yaw_mean;
                    fusion_pitch = leye_fusion_pitch;
                    fusion_yaw = leye_fusion_yaw;
                } else if (reye_gaze_type == GAZE_DISTRACTED_TYPE_NONE && has_reye_cali) {
                    cali_pitch = temp_righteye_gaze_pitch_mean;
                    cali_yaw = temp_righteye_gaze_yaw_mean;
                    fusion_pitch = reye_fusion_pitch;
                    fusion_yaw = reye_fusion_yaw;
                }
                break;
            default:
                std::cout << "not set fusion use eye..." << std::endl;
                break;
        }
        gaze_pitch_mean = cali_pitch;
        gaze_yaw_mean = cali_yaw;

        cali_pitch = ((face_info.isMask == 1) ? 0 : headpose_pitch_ * headpose_pitch_threshold) + cali_pitch * gaze_pitch_threshold;
        cali_yaw = ((face_info.isMask == 1) ? 0 : headpose_yaw_ * headpose_yaw_threshold) + cali_yaw * gaze_yaw_threshold;
    }

    return eye_gaze_type;
}

DistractionWarn::GazeDistractedType DistractionWarn::checkGazeDistraction(
    const TXDmsFaceInfo& face_info, bool is_curve) {
    GazeDistractedType gaze_result = GAZE_DISTRACTED_TYPE_UNDISTRACTED;
    predict_result = 0;
    mapping_x = 0.0f;
    mapping_y = 0.0f;

    if (auto_calibration) {
        float mapping_pitch = 0.0f;
        float mapping_yaw = 0.0f;
        cv::Point2f mapping_point(0.0f, 0.0f);
        double distance_to_hull = 0.0;
        float cali_pitch = 0.0f;
        float cali_yaw = 0.0f;

        gaze_result = calcPreMapping(face_info, mapping_yaw, mapping_pitch, cali_yaw, cali_pitch);

        // 投射点计算视线区域初始化
        if (region_init_flag == false) {
            if (CcSvmModel::getInstance()->init(region_mapping_width, region_mapping_height,
                                                region_hulls, tolerate_percentage) == 0) {
                CcSvmModel::getInstance()->set_cali_angle3d(cali_pitch, cali_yaw, headpose_roll_);
                region_init_flag = true;
            }
        } else {
            // 标定成功后一直进行标定修正
            if (is_loop_calibration) {
                CcSvmModel::getInstance()->set_cali_angle3d(cali_pitch, cali_yaw, headpose_roll_);
            }
            // 初始化已完成且所要求的眼睛可视
            if (gaze_result == GAZE_DISTRACTED_TYPE_NONE) {
                predict_result = CcSvmModel::getInstance()->predict(
                    mapping_pitch, mapping_yaw, headpose_roll_, 0, mapping_point,
                    distance_to_hull);  //0是index
                // 根据返回的区域索引来决定是否分神
                if (predict_result != 0) {
                    gaze_result = GAZE_DISTRACTED_TYPE_DISTRACTED;
                } else {
                    // std::cout << "distance_to_hull:" << distance_to_hull << " is_curve:" << is_curve
                    //           << " mapping_point.y:" << mapping_point.y << std::endl;
                    // if (distance_to_hull < 15.0 && !is_curve && mapping_point.y > 405) {
                    //     gaze_result = GAZE_DISTRACTED_TYPE_DISTRACTED_WITH_NOTCURVE;
                    // } else {
                    gaze_result = GAZE_DISTRACTED_TYPE_UNDISTRACTED;
                    // // }
                }
            }

            mapping_x = mapping_point.x;
            mapping_y = mapping_point.y;
        }
    }

    return gaze_result;
}

bool DistractionWarn::distractionProcess(const TXDmsFaceInfo& face_info,
                                         bool is_headpose_distracted,
                                         GazeDistractedType gaze_result,
                                         std::string& distraction_reason,
                                         const float right_up_down_proportion,
                                         const float left_up_down_proportion) {
    bool current_distraction_status = false;
    if (auto_calibration) {
        // 双眼都可视时，优先使用眼睛判断分心
        bool both_eyes_credible = isGazeCredible(face_info.left_eye_landmark) &&
            isGazeCredible(face_info.right_eye_landmark);
        if (both_eyes_credible && gaze_result == GAZE_DISTRACTED_TYPE_DISTRACTED) {
            current_distraction_status = true;
            distraction_reason = distraction_reason + "eyegaze";
        }
        // 增加眼睛曲率判断
        if (gaze_result == GAZE_DISTRACTED_TYPE_DISTRACTED_WITH_NOTCURVE) {
            current_distraction_status = true;
            distraction_reason = distraction_reason + "eyenotcurve";
        }

        if (!current_distraction_status) {
            bool is_mask = (face_info.isMask == 1) ? true : false;
            if (face_info.head_yaw > std::min(headgaze_yaw_l_offset, headgaze_yaw_r_offset) + ((is_mask) ? -HEAD_POSE_SPE_GLASSES_YAW_L : 0) &&
                face_info.head_yaw < std::max(headgaze_yaw_l_offset, headgaze_yaw_r_offset) + ((is_mask) ? HEAD_POSE_SPE_GLASSES_YAW_R : 0)) {
                if (gaze_result == GAZE_DISTRACTED_TYPE_DISTRACTED) {
                    current_distraction_status = true;
                    distraction_reason = distraction_reason + "eyegaze";
                } else if ((gaze_result == GAZE_DISTRACTED_TYPE_MODELNOTDET || gaze_result == GAZE_DISTRACTED_TYPE_INCREDIBLE) &&
                           is_headpose_distracted) {
                    current_distraction_status = true;
                    distraction_reason = distraction_reason + "headpose";
                }
            } else {
                if (is_headpose_distracted) {
                    current_distraction_status = true;
                    distraction_reason = distraction_reason + "headpose";
                }
            }
        }
    } else {
        if (is_headpose_distracted) {
            current_distraction_status = true;
            distraction_reason = distraction_reason + "headpose_EXP";
        } 
        else if (is_eyegaze_exp) {
            current_distraction_status = true;
            distraction_reason = distraction_reason + "eyegaze_EXP";
        }
    }

    if (auto_calibration) {
        if (is_eyegaze_exp && !current_distraction_status) {
            std::cout << "is_eyegaze_exp but !current_distraction_status ???" << distraction_reason << std::endl;
        }  else if (!is_eyegaze_exp && current_distraction_status) {
            std::cout << "!is_eyegaze_exp but current_distraction_status ???" << distraction_reason << std::endl;
        }
    }

    // 增加上一次分心原因记录
    last_distraction_reason = "";
    if (current_distraction_status) {
        if (distraction_reason.find("eyegaze") != std::string::npos) {
            last_distraction_reason = std::to_string(fusion_use_eye) + "_" + distraction_reason;
            if (mapping_x >= 400) {
                if (mapping_y >= 410)
                    last_distraction_reason += "right_lower";  //"left_upper";
                else
                    last_distraction_reason += "right_upper";  //"left_lower";
            } else if (mapping_x < 400) {
                if (mapping_y >= 410)
                    last_distraction_reason += "left_lower";  //"right_upper";
                else
                    last_distraction_reason += "left_upper";  //"right_lower";
            }
        }
        // std::cout << "last_distraction_reason:" << last_distraction_reason << std::endl;
    }

    if (face_info.right_eye_landmark.eye_score == -4 || face_info.left_eye_landmark.eye_score == -4) {
        current_distraction_status = false;
        distraction_reason = distraction_reason + "kp unstable";
    }

    return current_distraction_status;
}

void DistractionWarn::checkAbnormalDistraction(TXDmsFaceInfo& face_info,
                                               bool& current_distraction_status,
                                               std::string& headpose_reason,
                                               std::string& distraction_reason,
                                               float right_pupil_to_up_down_dis,
                                               float left_pupil_to_up_down_dis) {
    GazeDistractedType gaze_result = GAZE_DISTRACTED_TYPE_NONE;

    // 异常姿态做特异性补偿
    if (auto_calibration) {
#if defined(BYD_SC3E_R)
        constexpr float SPE_SITUATION_ROLL_DIFF = -30;
#else
        constexpr float SPE_SITUATION_ROLL_DIFF = -15;
#endif
        float roll_diff = face_info.head_roll - headpose_roll_;
        if (roll_diff < SPE_SITUATION_ROLL_DIFF) {
            face_info.head_yaw = headpose_yaw_;
            if (fusion_use_eye == FUSE_EYE_RIGHT) {
                face_info.right_eye_landmark.yaw = gaze_yaw_mean;
                face_info.right_eye_landmark.pitch += 1;
            } else if (fusion_use_eye == FUSE_EYE_LEFT) {
                face_info.left_eye_landmark.yaw = gaze_yaw_mean;
                face_info.left_eye_landmark.pitch += 1;
            }

            distraction_reason += " no4 spe roll diff ";
            gaze_result = checkGazeDistraction(face_info);
        }
    }

    // // 1.在脸转向摄像头侧时，近摄像头眼睛的瞳孔的像素移动会造成更大的gaze yaw differ,所以给一个debuff后再计算mapping情况
    // if ((headpose_reason.find("headyaw minlimit") != std::string::npos &&
    //      fusion_use_eye == FUSE_EYE_RIGHT) ||
    //     (headpose_reason.find("headyaw maxlimit") != std::string::npos &&
    //      fusion_use_eye == FUSE_EYE_LEFT)) {
    //     if (fusion_use_eye == FUSE_EYE_RIGHT &&
    //         face_info.right_eye_landmark.yaw - gaze_yaw_mean >
    //             0) {  // 异常姿态给一个矫正，但不能小于标定值
    //         face_info.head_yaw = std::max(float(face_info.head_yaw * 0.7), headpose_yaw_);
    //     } else if (fusion_use_eye == FUSE_EYE_LEFT &&
    //                face_info.left_eye_landmark.yaw - gaze_yaw_mean < 0) {  // 这个大于还是小于待确认
    //         face_info.head_yaw = std::max(float(face_info.head_yaw * 0.7), headpose_yaw_);
    //     }

    //     gaze_result = checkGazeDistraction(face_info);
    //     if (gaze_result == GAZE_DISTRACTED_TYPE_UNDISTRACTED) {
    //         current_distraction_status = false;
    //         distraction_reason = distraction_reason + " eyegaze_abnormal_1";
    //     }
    // }
    // // 2.低头且看前方驾驶时，需要减弱头部pitch的影响
    // if (current_distraction_status &&
    //     headpose_reason.find("headpitch minlimit") != std::string::npos) {
    //     //TODO:因为个体差异，opening值是否也弄一个标定得到的值？
    //     if (fusion_use_eye == FUSE_EYE_RIGHT) {
    //         if (right_pupil_to_up_down_dis < 0.45 && face_info.right_eye_landmark.opening > 0.45 &&
    //             (headpose_reason.find("headyaw minlimit") == std::string::npos &&
    //              headpose_reason.find("headyaw maxlimit") == std::string::npos)) {
    //             float pitch_diff = face_info.head_pitch - headpose_pitch_;
    //             face_info.head_pitch = headpose_pitch_ + pitch_diff * 0.5;
    //         } else if (face_info.right_eye_landmark.pitch - gaze_pitch_mean > -0.5) {
    //             face_info.head_pitch = std::max(float(face_info.head_pitch * 1.3), headpose_pitch_);
    //         }
    //     } else if (fusion_use_eye == FUSE_EYE_LEFT) {
    //         if (left_pupil_to_up_down_dis < 0.45 && face_info.left_eye_landmark.opening > 0.45 &&
    //             (headpose_reason.find("headyaw minlimit") == std::string::npos &&
    //              headpose_reason.find("headyaw maxlimit") == std::string::npos)) {
    //             float pitch_diff = face_info.head_pitch - headpose_pitch_;
    //             face_info.head_pitch = headpose_pitch_ + pitch_diff * 0.5;
    //         } else if (face_info.left_eye_landmark.pitch - gaze_pitch_mean > -0.5) {
    //             face_info.head_pitch = std::max(float(face_info.head_pitch * 1.3), headpose_pitch_);
    //         }
    //     }
    //     if (current_distraction_status) {
    //         gaze_result = checkGazeDistraction(face_info);
    //         if (gaze_result == GAZE_DISTRACTED_TYPE_UNDISTRACTED) {
    //             // current_distraction_status = false;
    //             // distraction_reason = distraction_reason + " eyegaze_abnormal_2"; //异常判断鲁棒性太差
    //         }
    //     }
    // }
    // // 3.偏头且看前方驾驶时，需要减弱头部pitch的影响
    // if (current_distraction_status && std::abs(face_info.head_roll) >= 25) {
    //     face_info.head_pitch = std::max(float(face_info.head_pitch * 0.7), headpose_pitch_);
    //     gaze_result = checkGazeDistraction(face_info);
    //     if (gaze_result == GAZE_DISTRACTED_TYPE_UNDISTRACTED) {
    //         current_distraction_status = false;
    //         distraction_reason = distraction_reason + " eyegaze_abnormal_3";
    //     }
    // }
}

void DistractionWarn::keepHistoryEyeGaze(TXEyeLandmark& eye_info,
                                         TXEyeLandmark& last_eye_landmark,
                                         int& eye_lost_count,
                                         int eye_flag) {
    // 眼睛瞳孔分数为0，且没有连续3帧为0，则使用上一帧保存的数据
    if (eye_info.pupil_score == 0) {  // 改为等于0，因为不包括模型输出后被一系列逻辑改成小于0的
        bool keep_flag = false;  // 对使用历史视线数据增加更精确的条件
        if (last_distraction_reason.find("eyegaze") != std::string::npos &&
            last_distraction_reason.find("lower") != std::string::npos) {
            if ((eye_flag == FUSE_EYE_LEFT &&
                 last_distraction_reason.find("0") != std::string::npos) ||
                (eye_flag == FUSE_EYE_RIGHT &&
                 last_distraction_reason.find("1") != std::string::npos)) {
                keep_flag = true;
            }
        }
        if (keep_flag && ++eye_lost_count <= 2) {
            eye_info = last_eye_landmark;
            eye_info.opening = 0.0f;
        }
    } else {
        last_eye_landmark = eye_info;
        eye_lost_count = 0;
    }
}

bool DistractionWarn::isEyeCurve(const TXDmsFaceInfo face_info,
                                 const distra_related_eyeinfo& related_eyeinfo) {
    if (!auto_calibration)
        return true;
    
    // 尝试在未检测到瞳孔时，也可以通过眼睑曲率报警
    // if (mapping_y-400 < kMinMappingYDiffer)
    //     return true;

    float roll_diff = std::abs(face_info.head_roll - headpose_roll_);
    float pitch_diff = std::abs(face_info.head_pitch - headpose_pitch_);
    float yaw_diff = std::abs(face_info.head_yaw - headpose_yaw_);

    if (roll_diff >= kMinRollDiffer /*|| pitch_diff >= kMinPitchDiffer*/ || yaw_diff >= kMinYawDiffer)
        return true;
    
    if (face_info.left_eye_landmark.eye_score < 0 || face_info.right_eye_landmark.eye_score < 0)
        return true;
        
    bool is_curve = true;
    float curve_score = 1.0f;
    float curve_score_mean_ = 1.0f;
    float offset_value = -1.0f;  //0-1
    float eye_pitch_offset = -1.0f;
    float offset_base = (1 - kCurveThreshold) * (KMaxCurveScore / 2);  //0.0003
    // 防止出现标定出极端值的情况
    if (leye_uper_curve_score_mean_ > KMaxCurveScore) {
        leye_uper_curve_score_mean_ = KMaxCurveScore;
    }
    if (reye_uper_curve_score_mean_ > KMaxCurveScore) {
        reye_uper_curve_score_mean_ = KMaxCurveScore;
    }

    curve_score = related_eyeinfo.leye_coutour_upper_curve_score;
    curve_score_mean_ = leye_uper_curve_score_mean_;
    eye_pitch_offset = (auto_calibration)
        ? (gaze_left_eye_pitch_ - face_info.left_eye_landmark.pitch - KEyePitchOffsetBase)
        : 0;
    if (curve_score < curve_score_mean_ && eye_pitch_offset > 0) {
        if (eye_pitch_offset >= KEyePitchMaxOffset) {
            offset_value = 1.0f;
        } else if (eye_pitch_offset < KEyePitchMaxOffset) {
            offset_value = eye_pitch_offset / KEyePitchMaxOffset;
        }
    }

    if (offset_value > 0 && (isGazeCredible(face_info.left_eye_landmark))) {
        if (face_info.left_eye_landmark.opening > kMinEyeOpening && curve_score > KMinCurveScore) {
            curve_score -= offset_base * offset_value;
            if (curve_score < curve_score_mean_ * kCurveThreshold)  // 0.0025*0.2 = 0.0005
                is_curve = false;
        }
    }
    if (is_curve) {
        curve_score = related_eyeinfo.reye_coutour_upper_curve_score;
        curve_score_mean_ = reye_uper_curve_score_mean_;
        offset_value = -1.0f;
        eye_pitch_offset = -1.0f;

        eye_pitch_offset = (auto_calibration)
            ? (gaze_right_eye_pitch_ - face_info.right_eye_landmark.pitch - KEyePitchOffsetBase)
            : 0;
        if (curve_score < curve_score_mean_ && eye_pitch_offset > 0) {
            if (eye_pitch_offset >= KEyePitchMaxOffset) {
                offset_value = 1.0f;
            } else if (eye_pitch_offset < KEyePitchMaxOffset) {
                offset_value = eye_pitch_offset / KEyePitchMaxOffset;
            }
        }
        if (offset_value > 0 && (isGazeCredible(face_info.right_eye_landmark))) {
            if (face_info.right_eye_landmark.opening > kMinEyeOpening &&
                curve_score > KMinCurveScore) {
                curve_score -= offset_base * offset_value;
                if (curve_score < curve_score_mean_ * kCurveThreshold)
                    is_curve = false;
            }
        }
    }
    bool left_eye_not_curve = (face_info.left_eye_landmark.eye_score > 0 &&
                               face_info.left_eye_landmark.pupil_score <= 0 &&
                               related_eyeinfo.leye_coutour_upper_curve_score < KMinCurveScore
                               && face_info.left_eye_landmark.opening > 0.1);
    bool right_eye_not_curve = (face_info.right_eye_landmark.eye_score > 0 &&
                                face_info.right_eye_landmark.pupil_score <= 0 &&
                                related_eyeinfo.reye_coutour_upper_curve_score < KMinCurveScore
                                && face_info.right_eye_landmark.opening > 0.1);
    if (left_eye_not_curve || right_eye_not_curve) {
        is_curve = false;
    }

    // 状态管理：根据当前帧结果更新计数器
    if (!is_curve) {  // 当前帧检测到异常（眼睑曲率不正常）
        continuous_abnormal_count_++;
        continuous_normal_count_ = 0;  // 重置正常计数

        // 连续异常帧数达到阈值，激活状态
        if (continuous_abnormal_count_ >= kMinContinuousFrames) {
            eye_curve_active_ = true;
        }
    } else {  // 当前帧检测正常
        continuous_normal_count_++;
        continuous_abnormal_count_ = 0;  // 重置异常计数

        // 连续正常帧数达到阈值，取消激活状态
        if (continuous_normal_count_ >= kMinContinuousFrames) {
            eye_curve_active_ = false;
        }
    }

    // 返回当前状态标志位（true表示正常，false表示异常）
    // std::cout << "curve_score:" << curve_score
    //           << " curve_score_thr:" << curve_score_mean_ * 0.88 << std::endl;
    return !eye_curve_active_;  // 注意：返回值需要取反，因为active=true表示检测到异常
}

bool DistractionWarn::IsDistracted(TXDmsFaceInfo face_info,
                                   const TXCarInfo* car_info,
                                   const distra_related_eyeinfo& related_eyeinfo,
                                   bool is_face_keypoints_valid,
                                   long now_ts) {
    current_distraction_status = false;
    distraction_reason = "";
    face_box_ratio_ = -1.0f;
    bool is_face_valid = isFaceValid(face_info);
    if (is_face_valid)
    {
        // 计算人脸框宽高比：宽度/高度
        int face_width = face_info.xmax - face_info.xmin;
        int face_height = face_info.ymax - face_info.ymin;
        face_box_ratio_ = (face_height > 0) ?
            static_cast<float>(face_width) / static_cast<float>(face_height) : -1.0f;            
    }
                                    
    // std::cout << "is_face_valid:" << is_face_valid << " is_face_keypoints_valid:" << is_face_keypoints_valid << std::endl;
    // 在戴口罩情况下，可能人脸关键点置信度不够，导致无法进入判断主流程！ 
    is_face_keypoints_valid = is_face_keypoints_valid || face_info.isMask == 1;                         
    if (is_face_valid && is_face_keypoints_valid) {
        // 解耦将上一层的处理移入分心模块
        keepHistoryEyeGaze(face_info.left_eye_landmark, last_left_eye_landmark, left_eye_lost_count,
                           FUSE_EYE_LEFT);
        keepHistoryEyeGaze(face_info.right_eye_landmark, last_right_eye_landmark,
                           right_eye_lost_count, FUSE_EYE_RIGHT);

        updateHeadYawBias(face_info, now_ts);
        if (isSteeringAngleInvalid(car_info, distraction_reason)) {
            return false;
        }

        HeadRotationRange head_rotation_range = {0};
        head_rotation_range = calcNonDistractHeadRotation(car_info, face_info);

        if (checkFalseDistraction(face_info, head_rotation_range, distraction_reason)) {
            return false;
        }
        headpose_reason = "";
        bool is_headpose_distracted =
            checkHeadPoseDistraction(face_info, head_rotation_range, headpose_reason);

        bool is_curve = true;
        auto gaze_result = checkGazeDistraction(face_info, is_curve);
        is_curve = isEyeCurve(face_info, related_eyeinfo);
        if (gaze_result <= GAZE_DISTRACTED_TYPE_UNDISTRACTED && !is_curve) {
            gaze_result = GAZE_DISTRACTED_TYPE_DISTRACTED_WITH_NOTCURVE;
        }
        // std::cout << "is_curve:" << is_curve << "gaze_result:" << gaze_result << std::endl;
        current_distraction_status = distractionProcess(
            face_info, is_headpose_distracted, gaze_result, distraction_reason,
            related_eyeinfo.right_up_down_proportion, related_eyeinfo.left_up_down_proportion);

        // 增加异常姿态的分心误检抑制
        if (current_distraction_status && distraction_reason.find("eyegaze") != std::string::npos) {
            checkAbnormalDistraction(face_info, current_distraction_status, headpose_reason,
                                     distraction_reason, related_eyeinfo.right_pupil_to_up_down_dis,
                                     related_eyeinfo.left_pupil_to_up_down_dis);
        }

    } 

    if (!current_distraction_status &&
        distraction_reason.find("eyegaze_abnormal") == std::string::npos) {
        distraction_reason = "no_distraction";
    }
    // std::cout << "2 current_distraction_status:" << current_distraction_status << "distraction_reason:" << distraction_reason << std::endl;
    return current_distraction_status;
}

void DistractionWarn::StartCalibration(const bool is_mask,
                                       float head_pose_yaw,
                                       float head_pose_pitch,
                                       float head_pose_roll,
                                       float gaze_left_yaw,
                                       float gaze_left_pitch,
                                       float gaze_right_yaw,
                                       float gaze_right_pitch,
                                       float left_eye_conf,
                                       float right_eye_conf,
                                       const float leye_uper_curve_score,
                                       const float reye_uper_curve_score) {
    is_eyegaze_exp = false;

    // StartCalibration内部处理，眼部数据质量检查

    // 首先计算眼角距离
    calculateEyeCornerDistances();

    // 检查校准条件是否满足
    CalibrationConditionResult condition_result = isCalibrationConditionMet();
                   
    if (left_eye_conf < 1 || !condition_result.left_pupil_position_valid) {
        gaze_left_yaw = -9999.0f;
        gaze_left_pitch = -9999.0f;
    }
    if (right_eye_conf < 1 || !condition_result.right_pupil_position_valid) {
        gaze_right_yaw = -9999.0f;
        gaze_right_pitch = -9999.0f;
    }
    bool is_headpose_valid = condition_result.right_eye_valid && condition_result.left_eye_valid &&
                             condition_result.inner_distance_valid && condition_result.ratio_valid &&
                              condition_result.far_eye_distance_valid;

    // 眼部几何约束检查已完成
    bool is_head_assit_valid = false;
    switch (fusion_use_eye) {
        case FUSE_EYE_LEFT:
            is_head_assit_valid = (left_eye_conf >= 1 && (condition_result.pupil_state.left_eye_state != PupilState::NORMAL));
            if (left_eye_conf < 1) 
                is_eyegaze_exp = false;
            break;
        case FUSE_EYE_RIGHT:
            is_head_assit_valid = (right_eye_conf >= 1 && (condition_result.pupil_state.right_eye_state != PupilState::NORMAL));
            if (right_eye_conf < 1) 
                is_eyegaze_exp = false;            
            break;
        case FUSE_EYE_BOTH:
            is_head_assit_valid = (left_eye_conf >= 1 && (condition_result.pupil_state.left_eye_state != PupilState::NORMAL)) || 
                                        (right_eye_conf >= 1 && (condition_result.pupil_state.right_eye_state != PupilState::NORMAL));
            if (left_eye_conf < 1 || right_eye_conf < 1) 
                is_eyegaze_exp = false;
            break;
        default:
            is_head_assit_valid = false;
            break;
    }
    if (distraction_reason.find("no_distraction2") != std::string::npos) {
        is_head_assit_valid = true;
    }

    // 如果mask为true，则暂时不进行头部角度的标定
    if (is_mask || !is_headpose_valid || is_head_assit_valid) { 
        head_pose_pitch = -9999.0f;
        head_pose_yaw = -9999.0f;
        head_pose_roll = -9999.0f;
    }

    std::vector<float> head_angles = {head_pose_pitch, head_pose_yaw, head_pose_roll};
    std::vector<float> lefteye_angles = {gaze_left_pitch, gaze_left_yaw / 10};
    std::vector<float> righteye_angles = {gaze_right_pitch, gaze_right_yaw / 10};
    std::vector<float> centroid;
    std::vector<float> leyecentroid;
    std::vector<float> reyecentroid;
    
    float leye_uper_curve_score_mean = 1.0f;
    float reye_uper_curve_score_mean = 1.0f;
    if (calibrator->execute(head_angles, lefteye_angles, righteye_angles, leye_uper_curve_score,
                            reye_uper_curve_score, centroid, leyecentroid, reyecentroid,
                            leye_uper_curve_score_mean, reye_uper_curve_score_mean, cali_status)) {
        if (cali_status.head_cali_finish) {
            has_head_cali = true;
            temp_headpose_yaw_mean = centroid[1];
            temp_headpose_pitch_mean = centroid[0];
            temp_headpose_roll_mean = centroid[2];
        }
        if (cali_status.leye_cali_finish) {
            has_leye_cali = true;
            temp_lefteye_gaze_pitch_mean = leyecentroid[0];
            temp_lefteye_gaze_yaw_mean = leyecentroid[1] * 10;
            if (is_leye_first_cali_success) {
                is_leye_first_cali_success = false;
                leye_uper_curve_score_mean_ = leye_uper_curve_score_mean;
                std::cout << "leye_uper_curve_score_mean_:" << leye_uper_curve_score_mean_
                            << std::endl;
            }
        }
        if (cali_status.reye_cali_finish) {
            has_reye_cali = true;
            temp_righteye_gaze_pitch_mean = reyecentroid[0];
            temp_righteye_gaze_yaw_mean = reyecentroid[1] * 10;
            if (is_reye_first_cali_success) {
                is_reye_first_cali_success = false;
                reye_uper_curve_score_mean_ = reye_uper_curve_score_mean;
                std::cout << "reye_uper_curve_score_mean_:" << reye_uper_curve_score_mean_
                            << std::endl;
            }
        }
    }
    
    return;
}

void DistractionWarn::ClearCalibration() {
    auto_calibration = false;
    has_head_cali = false;
    has_leye_cali = false;
    has_reye_cali = false;

    // 清空从标定模块获取的数据
    temp_headpose_pitch_mean = -9999.0f;
    temp_headpose_yaw_mean = -9999.0f;
    temp_headpose_roll_mean = -9999.0f;
    temp_lefteye_gaze_pitch_mean = -9999.0f;
    temp_lefteye_gaze_yaw_mean = -9999.0f;
    temp_righteye_gaze_pitch_mean = -9999.0f;
    temp_righteye_gaze_yaw_mean = -9999.0f;
    leye_uper_curve_score_mean_ = 1.0f;
    reye_uper_curve_score_mean_ = 1.0f;
    is_leye_first_cali_success = true;
    is_reye_first_cali_success = true;
    head_yaw_3s_vec.clear();

    // 重置眼睑曲率状态管理变量
    continuous_abnormal_count_ = 0;
    continuous_normal_count_ = 0;
    eye_curve_active_ = false;

    calibrator->clear();
    CcSvmModel::getInstance()->clear();

    region_init_flag = false;
}

void DistractionWarn::QuickFillingCaliData() {
    static bool is_data_loaded = false;
    std::string config_file = "calidata.json";
    if (!is_data_loaded && access(config_file.c_str(), F_OK) == 0) {
        Json::Reader json_reader;
        Json::Value root;
        std::ifstream infile(config_file, std::ios::binary);
        if (!infile.is_open()) {
            std::cout << "Open  sight config file failed!" << std::endl;
        }

        if (!json_reader.parse(infile, root)) {
            std::cout << "Parse  sight json config file failed!" << std::endl;
        } else {
            is_data_loaded = true;
            std::vector<float> file_head_angles = {root["head_pitch"].asFloat(),
                                                   root["head_yaw"].asFloat(),
                                                   root["head_roll"].asFloat()};
            std::vector<float> file_lefteye_angles = {root["left_eye_pitch"].asFloat(),
                                                      root["left_eye_yaw"].asFloat() / 10};
            std::vector<float> file_righteye_angles = {root["right_eye_pitch"].asFloat(),
                                                       root["right_eye_yaw"].asFloat() / 10};
            float file_leye_curve_mean = root["left_eye_curve_mean"].asFloat();
            float file_reye_curve_mean = root["right_eye_curve_mean"].asFloat();

            calibrator->quickFilling(file_head_angles, file_lefteye_angles, file_righteye_angles,
                                     file_leye_curve_mean, file_reye_curve_mean);

            std::cout << "Cali data filling ok!" << std::endl;
            std::cout << "Head angles(yaw/pitch/roll): " << file_head_angles[1] << ", "
                      << file_head_angles[0] << ", " << file_head_angles[2] << std::endl;
            std::cout << "Left eye angles(yaw/pitch): " << file_lefteye_angles[1] * 10 << ", "
                      << file_lefteye_angles[0] << std::endl;
            std::cout << "Right eye angles(yaw/pitch): " << file_righteye_angles[1] * 10 << ", "
                      << file_righteye_angles[0] << std::endl;
            std::cout << "Left eye curve score:" << file_leye_curve_mean << std::endl;
            std::cout << "Right eye curve score:" << file_reye_curve_mean << std::endl;
        }
    }

    return;
}

bool DistractionWarn::GetCalibrationStatus() {
    return auto_calibration;
}

void DistractionWarn::GetHeadPosePitch(float& min_value, float& max_value) {
    if (read_json) {
        min_value = headpose_pitch_ - pitch_down;
        max_value = headpose_pitch_ + pitch_up;
    } else {
        min_value = headpose_pitch_ - PITCH_DOWN;
        max_value = headpose_pitch_ + PITCH_UP;
    }
    return;
}

void DistractionWarn::GetHeadPoseYaw(float& min_value, float& max_value) {
    if (read_json) {
        min_value = headpose_yaw_ - yaw_left;
        max_value = headpose_yaw_ + yaw_right;
    } else {
        min_value = headpose_yaw_ - YAW_LEFT;
        max_value = headpose_yaw_ + YAW_RIGHT;
    }
    return;
}

void DistractionWarn::GetHeadPoseRoll(float& min_value, float& max_value) {
    if (read_json) {
        min_value = headpose_roll_ - roll_left;
        max_value = headpose_roll_ + roll_right;
    } else {
        min_value = headpose_roll_ - ROLL_LIFT;
        max_value = headpose_roll_ + ROLL_RIGHT;
    }
    return;
}

void DistractionWarn::getNonDistractHeadRotation(float& headyaw_min,
                                                 float& headyaw_max,
                                                 float& headpitch_min,
                                                 float& headpitch_max) {
    if (auto_calibration) {
        headyaw_min = distract_param.head_yaw_min;
        headyaw_max = distract_param.head_yaw_max;
        headpitch_min = distract_param.head_pitch_min;
        headpitch_max = distract_param.head_pitch_max;

    } else {
        headyaw_min = -90;
        headyaw_max = 90;
        headpitch_min = -90;
        headpitch_max = 90;
    }
}

std::string DistractionWarn::GetDistractParamers() {
    std::stringstream ss;
    ss << "{";
    ss << "\"head_yaw_min\":" << distract_param.head_yaw_min << ",";
    ss << "\"head_yaw_max\":" << distract_param.head_yaw_max << ",";
    ss << "\"head_pitch_min\":" << distract_param.head_pitch_min << ",";
    ss << "\"head_pitch_max\":" << distract_param.head_pitch_max << ",";
    // ss << "\"right_gaze_vf_yaw_min\":" << distract_param.right_gaze_vf_yaw_min << ",";
    // ss << "\"right_gaze_vf_yaw_max\":" << distract_param.right_gaze_vf_yaw_max << ",";
    // ss << "\"right_gaze_vf_pitch_min\":" << distract_param.right_gaze_vf_pitch_min << ",";
    // ss << "\"right_gaze_vf_pitch_max\":" << distract_param.right_gaze_vf_pitch_max << ",";

    // ss << "\"left_gaze_vf_yaw_min\":" << distract_param.left_gaze_vf_yaw_min << ",";
    // ss << "\"left_gaze_vf_yaw_max\":" << distract_param.left_gaze_vf_yaw_max << ",";
    // ss << "\"left_gaze_vf_pitch_min\":" << distract_param.left_gaze_vf_pitch_min << ",";
    // ss << "\"left_gaze_vf_pitch_max\":" << distract_param.left_gaze_vf_pitch_max << ",";
    ss << "\"headpose_pitch_\":" << headpose_pitch_ << ",";
    ss << "\"headpose_yaw_\":" << headpose_yaw_ << ",";
    ss << "\"headpose_roll_\":" << headpose_roll_ << ",";
    ss << "\"gaze_pitch_mean\":" << gaze_pitch_mean << ",";
    ss << "\"gaze_yaw_mean\":" << gaze_yaw_mean << ",";

    ss << "\"predict_result\":" << predict_result << ",";
    ss << "\"mapping_x\":" << mapping_x << ",";
    ss << "\"mapping_y\":" << mapping_y << ",";

    ss << "\"current_head_yaw_bias\":" << distract_param.head_yaw_bias << ",";

    ss << "\"current_right_gaze_vf_yaw\":" << distract_param.current_right_gaze_vf_yaw << ",";
    ss << "\"current_right_gaze_vf_pitch\":" << distract_param.current_right_gaze_vf_pitch << ",";
    ss << "\"current_left_gaze_vf_yaw\":" << distract_param.current_left_gaze_vf_yaw << ",";
    ss << "\"current_left_gaze_vf_pitch\":" << distract_param.current_left_gaze_vf_pitch << ",";
    ss << "\"leye_uper_curve_score_mean_\":" << leye_uper_curve_score_mean_ << ",";
    ss << "\"reye_uper_curve_score_mean_\":" << reye_uper_curve_score_mean_ << ",";

    ss << "\"headrange_center_yaw\":" << headrange_center_yaw << ",";
    ss << "\"headrange_center_pitch\":" << headrange_center_pitch << ",";
    ss << "\"headrange_a\":" << headrange_a << ",";
    ss << "\"headrange_b\":" << headrange_b << ",";

    // 添加 region_hulls
    ss << "\"region_hulls\":[";
    for (size_t i = 0; i < region_hulls.size(); ++i) {
        ss << "[";
        for (size_t j = 0; j < region_hulls[i].size(); ++j) {
            ss << "{\"x\":" << region_hulls[i][j].x << ",\"y\":" << region_hulls[i][j].y << "}";
            if (j < region_hulls[i].size() - 1) {
                ss << ",";
            }
        }
        ss << "]";
        if (i < region_hulls.size() - 1) {
            ss << ",";
        }
    }
    ss << "]";

    ss << "}";
    return ss.str();
}

void DistractionWarn::GetDistractionInfo(internal_analysis_distraction_info& info) {
    info = distraction_info;
}

// 关键点数据处理函数实现
void DistractionWarn::SetFaceKeypoints(const std::shared_ptr<std::vector<cv::Point2f>>& face_keypoints) {
    current_face_keypoints_ = face_keypoints;
    TX_LOG_DEBUG("DistractionWarn", "Face keypoints updated, count: %d", face_keypoints ? (int)face_keypoints->size() : 0);
}

void DistractionWarn::SetEyeKeypoints(const std::shared_ptr<std::vector<cv::Point2f>>& eye_keypoints) {
    current_eye_keypoints_ = eye_keypoints;
    TX_LOG_DEBUG("DistractionWarn", "Eye keypoints updated, count: %d", eye_keypoints ? (int)eye_keypoints->size() : 0);
}

void DistractionWarn::ProcessKeypoints(const std::shared_ptr<std::vector<cv::Point2f>>& face_keypoints,
                                      const std::shared_ptr<std::vector<cv::Point2f>>& eye_keypoints,
                                      long timestamp) {
    // 存储关键点数据
    current_face_keypoints_ = face_keypoints;
    current_eye_keypoints_ = eye_keypoints;
    keypoints_timestamp_ = timestamp;
    
    TX_LOG_DEBUG("DistractionWarn", "ProcessKeypoints called with timestamp: %ld", timestamp);
    
    // 处理人脸关键点
    if (face_keypoints && !face_keypoints->empty()) {
        TX_LOG_DEBUG("DistractionWarn", "Received %d face keypoints", (int)face_keypoints->size());
    }
    
    // 处理眼睛关键点
    if (eye_keypoints && !eye_keypoints->empty()) {
        TX_LOG_DEBUG("DistractionWarn", "Received %d eye keypoints", (int)eye_keypoints->size());
        
        // 假设眼睛关键点格式：前17个点为右眼，后17个点为左眼
        if (eye_keypoints->size() >= 34) {
            // 处理右眼关键点（前17个点）
            std::vector<cv::Point2f> right_eye_points(eye_keypoints->begin(), eye_keypoints->begin() + 17);
            
            // 处理左眼关键点（后17个点）  
            std::vector<cv::Point2f> left_eye_points(eye_keypoints->begin() + 17, eye_keypoints->end());
            
            TX_LOG_DEBUG("DistractionWarn", "Right eye points: %d, Left eye points: %d", 
                        (int)right_eye_points.size(), (int)left_eye_points.size());
        }
    }
    
    TX_LOG_DEBUG("DistractionWarn", "ProcessKeypoints completed");
}

// 计算眼角距离的函数实现
void DistractionWarn::calculateEyeCornerDistances() {
    if (!current_eye_keypoints_ || current_eye_keypoints_->empty()) {
        TX_LOG_WARN("Distraction", "No eye keypoints available for distance calculation");
        return;
    }
    // 根据眼睛关键点格式：前17个点为右眼，后17个点为左眼
    // 眼睛关键点索引约定：
    // 对于每只眼：0-7为轮廓点，8-15为虹膜轮廓点，16为瞳孔点
    // 通常眼角在轮廓点中：内眼角索引0，外眼角索引4
    if (current_eye_keypoints_->size() >= 34) {
        // 右眼关键点（前17个）
        cv::Point2f right_inner_corner = (*current_eye_keypoints_)[0];    // 右眼内眼角
        cv::Point2f right_outer_corner = (*current_eye_keypoints_)[4];    // 右眼外眼角
        // std::cout << "right_corner:" << right_inner_corner << " " << right_outer_corner << std::endl;
        // 左眼关键点（后17个）
        cv::Point2f left_inner_corner = (*current_eye_keypoints_)[17];    // 左眼内眼角  
        cv::Point2f left_outer_corner = (*current_eye_keypoints_)[21];    // 左眼外眼角
        // std::cout << "left_corner:" << left_inner_corner << " " << left_outer_corner << std::endl;
        // 计算右眼内外眼角距离
        right_eye_inner_outer_distance_ = cv::norm(right_inner_corner - right_outer_corner);
        
        // 计算左眼内外眼角距离
        left_eye_inner_outer_distance_ = cv::norm(left_inner_corner - left_outer_corner);
        
        // 计算左右眼内眼角距离
        inner_corners_distance_ = cv::norm(right_inner_corner - left_inner_corner);
        
        // std::cout << "Eye corner distances - Right eye: " << right_eye_inner_outer_distance_ <<
        //      ", Left eye: " << left_eye_inner_outer_distance_ << ", Inner corners: " << inner_corners_distance_ << std::endl;
    } else {
        std::cout << "Insufficient eye keypoints for corner distance calculation: " 
            << (int)current_eye_keypoints_->size() << std::endl;
    }
}

// 判断标定条件是否满足
DistractionWarn::CalibrationConditionResult DistractionWarn::isCalibrationConditionMet() {
    // 定义合理的眼角距离范围
    static constexpr float MIN_EYE_WIDTH = 30.0f;     // 最小眼睛宽度（像素）
    static constexpr float MAX_EYE_WIDTH = 60.0f;    // 最大眼睛宽度（像素）
    static constexpr float MIN_INNER_DISTANCE = 60.0f; // 最小内眼角距离（像素）
    static constexpr float MAX_INNER_DISTANCE = 150.0f; // 最大内眼角距离（像素）
    static constexpr float MIN_FAR_EYE_DISTANCE = 32.0f; // 远眼最小距离（像素）
    static constexpr float MIN_EYE_RATIO = 1.0f;        // 近眼/远眼比值最小值
    static constexpr float MAX_EYE_RATIO = 1.4f;        // 近眼/远眼比值最大值
    
    // 基础距离检查
    bool right_eye_valid = (right_eye_inner_outer_distance_ >= MIN_EYE_WIDTH && 
                           right_eye_inner_outer_distance_ <= MAX_EYE_WIDTH);
    bool left_eye_valid = (left_eye_inner_outer_distance_ >= MIN_EYE_WIDTH && 
                          left_eye_inner_outer_distance_ <= MAX_EYE_WIDTH);
    bool inner_distance_valid = (inner_corners_distance_ >= MIN_INNER_DISTANCE && 
                                inner_corners_distance_ <= MAX_INNER_DISTANCE);
    
    // 对于A柱摄像头，右眼是near_eye，左眼是far_eye
    float near_eye_x_pixel = 0.0f;
    float far_eye_x_pixel = 0.0f;
    if (fusion_use_eye == FUSE_EYE_RIGHT) {
        near_eye_x_pixel = right_eye_inner_outer_distance_;  // 右眼（近）
        far_eye_x_pixel = left_eye_inner_outer_distance_;    // 左眼（远）
    } else if (fusion_use_eye == FUSE_EYE_LEFT) {
        near_eye_x_pixel = left_eye_inner_outer_distance_;  // 左眼（近）
        far_eye_x_pixel = right_eye_inner_outer_distance_;    // 右眼（远）
    }
    
    // 条件1: 近眼/远眼比值范围判断
    bool ratio_valid = false;
    if (far_eye_x_pixel > 0.0f) {
        float ratio = near_eye_x_pixel / far_eye_x_pixel;
        ratio_valid = (ratio >= MIN_EYE_RATIO && ratio <= MAX_EYE_RATIO);
    }
    
    // 条件2: 远眼距离判断
    bool far_eye_distance_valid = (far_eye_x_pixel > MIN_FAR_EYE_DISTANCE);
    
    // 条件3: 瞳孔位置判断（使用归一化范围，支持三种状态）
#if defined(BYD_SC3E_R)
    const float normal_x_min = 0.55f;  // 瞳孔距离最左眼角为眼宽的65%
    const float normal_x_max = 0.9f;   // 瞳孔距离最左眼角为眼宽的100%
    const float normal_y_min = -0.4f;  // 瞳孔在眼角连线上方40%眼宽的位置
    const float normal_y_max = -0.2f;  // 瞳孔在眼角连线上方20%眼宽的位置
#else
    const float normal_x_min = 0.65f;  // 瞳孔距离最左眼角为眼宽的65%
    const float normal_x_max = 1.0f;   // 瞳孔距离最左眼角为眼宽的100%
    const float normal_y_min = -0.4f;  // 瞳孔在眼角连线上方40%眼宽的位置
    const float normal_y_max = -0.2f;  // 瞳孔在眼角连线上方20%眼宽的位置
#endif
    
    // 安全距离：在正常范围的各个方向外扩展的安全边距，用于区分"其它类型"和"分心"
    const float safety_margin_left = 0.15f;   // 左边安全距离
    const float safety_margin_right = 0.20f;  // 右边安全距离
    const float safety_margin_up = 0.08f;     // 上边安全距离
    const float safety_margin_down = 0.15f;   // 下边安全距离
    
    EyesPupilState eyes_pupil_state = checkPupilState(normal_x_min, normal_x_max, normal_y_min, normal_y_max,
                                                     safety_margin_left, safety_margin_right, 
                                                     safety_margin_up, safety_margin_down);

    // 如果处于大幅度的摇头晃脑过程中认为此时的瞳孔状态判断是不确定的
    if (distraction_reason.find("no_distraction2") != std::string::npos) {
        eyes_pupil_state.left_eye_state = PupilState::UNCERTAIN;
        eyes_pupil_state.right_eye_state = PupilState::UNCERTAIN;
    }

    // 分别计算左右眼瞳孔位置是否有效
    bool left_pupil_position_valid = (eyes_pupil_state.left_eye_state == PupilState::NORMAL);
    bool right_pupil_position_valid = (eyes_pupil_state.right_eye_state == PupilState::NORMAL);
    
    // 用于标定完成前的分心判定以及标定完成后的辅佐判断
    // 根据当前fusion_eye配置来判断是否分心
    bool is_current_eye_distracted = false;
    switch (fusion_use_eye) {
        case FUSE_EYE_LEFT:
            is_current_eye_distracted = (eyes_pupil_state.left_eye_state == PupilState::DISTRACTED);
            break;
        case FUSE_EYE_RIGHT:
            is_current_eye_distracted = (eyes_pupil_state.right_eye_state == PupilState::DISTRACTED);
            break;
        case FUSE_EYE_BOTH:
            is_current_eye_distracted = (eyes_pupil_state.left_eye_state == PupilState::DISTRACTED || 
                                        eyes_pupil_state.right_eye_state == PupilState::DISTRACTED);
            break;
        default:
            is_current_eye_distracted = false;
            break;
    }
    
    if (is_current_eye_distracted) {
        is_eyegaze_exp = true;
    }
    
    // 对于标定条件，根据fusion_use_eye配置决定如何判断瞳孔位置是否有效
    bool pupil_position_valid = false;
    switch (fusion_use_eye) {
        case FUSE_EYE_LEFT:
            pupil_position_valid = left_pupil_position_valid;
            break;
        case FUSE_EYE_RIGHT:
            pupil_position_valid = right_pupil_position_valid;
            break;
        case FUSE_EYE_BOTH:
            pupil_position_valid = (left_pupil_position_valid && right_pupil_position_valid);
            break;
        default:
            pupil_position_valid = false;
            break;
    }

    
    CalibrationConditionResult result;
    result.condition_met = right_eye_valid && left_eye_valid && inner_distance_valid && 
                          ratio_valid && far_eye_distance_valid && pupil_position_valid;
    result.pupil_state = eyes_pupil_state;
    result.right_eye_valid = right_eye_valid;
    result.left_eye_valid = left_eye_valid;
    result.inner_distance_valid = inner_distance_valid;
    result.ratio_valid = ratio_valid;
    result.far_eye_distance_valid = far_eye_distance_valid;
    result.left_pupil_position_valid = left_pupil_position_valid;
    result.right_pupil_position_valid = right_pupil_position_valid;
    result.pupil_position_valid = pupil_position_valid;
    
    // const char* pupil_state_names[] = {"NORMAL", "UNCERTAIN", "DISTRACTED"};
    // std::cout << "Calibration condition check - Right: " << (right_eye_valid ? "OK" : "FAIL") 
    // << " (" << right_eye_inner_outer_distance_ << "), Left: " << (left_eye_valid ? "OK" : "FAIL") 
    // << " (" << left_eye_inner_outer_distance_ << "), Inner: " << (inner_distance_valid ? "OK" : "FAIL") 
    // << " (" << inner_corners_distance_ << "), Ratio: " << (ratio_valid ? "OK" : "FAIL") 
    // << " (" << (far_eye_x_pixel > 0.0f ? near_eye_x_pixel / far_eye_x_pixel : 0.0f) << "), FarEye: " << (far_eye_distance_valid ? "OK" : "FAIL") 
    // << " (" << far_eye_x_pixel << "), Pupil L:" << pupil_state_names[static_cast<int>(eyes_pupil_state.left_eye_state)] 
    // << "(" << (left_pupil_position_valid ? "OK" : "FAIL") << ") R:" << pupil_state_names[static_cast<int>(eyes_pupil_state.right_eye_state)]
    // << "(" << (right_pupil_position_valid ? "OK" : "FAIL") << "), Combined: " << (pupil_position_valid ? "OK" : "FAIL") 
    // << ", Result: " << (result.condition_met ? "CONTINUE" : "STOP") << std::endl;
    
    return result;
}

// 检查瞳孔位置状态 - 基于正常范围+四个方向安全距离的三层区域判断
// 返回左右眼的独立状态
DistractionWarn::EyesPupilState DistractionWarn::checkPupilState(float normal_x_min, float normal_x_max, float normal_y_min, float normal_y_max,
                                                                 float safety_margin_left, float safety_margin_right, 
                                                                 float safety_margin_up, float safety_margin_down) const {
    // 参数有效性检查
    if (normal_x_min >= normal_x_max || normal_y_min >= normal_y_max) {
        TX_LOG_ERROR("Distraction", "Invalid normal range: x[%.2f,%.2f], y[%.2f,%.2f]", 
                     normal_x_min, normal_x_max, normal_y_min, normal_y_max);
        return EyesPupilState(PupilState::UNCERTAIN, PupilState::UNCERTAIN);
    }
    if (safety_margin_left < 0.0f || safety_margin_right < 0.0f || 
        safety_margin_up < 0.0f || safety_margin_down < 0.0f) {
        TX_LOG_ERROR("Distraction", "Invalid safety margins: left=%.2f, right=%.2f, up=%.2f, down=%.2f (should be non-negative)", 
                     safety_margin_left, safety_margin_right, safety_margin_up, safety_margin_down);
        return EyesPupilState(PupilState::UNCERTAIN, PupilState::UNCERTAIN);
    }
    
    // 计算三层区域的边界
    // 1. 正常范围（最内层）
    // 已由参数直接提供：normal_x_min, normal_x_max, normal_y_min, normal_y_max
    
    // 2. 安全距离范围（中间层）= 正常范围 + 各方向安全距离
    float safe_x_min = normal_x_min - safety_margin_left;   // 左边界向左扩展
    float safe_x_max = normal_x_max + safety_margin_right;  // 右边界向右扩展
    float safe_y_min = normal_y_min - safety_margin_down;   // 下边界向下扩展（y值更小）
    float safe_y_max = normal_y_max + safety_margin_up;     // 上边界向上扩展（y值更大）
    
    // 3. 超出安全距离范围的都是分心区域（无需定义边界）
    
    if (!current_eye_keypoints_ || current_eye_keypoints_->empty()) {
        TX_LOG_WARN("Distraction", "No eye keypoints available for pupil state check");
        return EyesPupilState(PupilState::UNCERTAIN, PupilState::UNCERTAIN);
    }
    
    // 根据眼睛关键点格式：前17个点为右眼，后17个点为左眼
    if (current_eye_keypoints_->size() < 34) {
        TX_LOG_WARN("Distraction", "Insufficient eye keypoints for pupil state check: %d", 
                   (int)current_eye_keypoints_->size());
        return EyesPupilState(PupilState::UNCERTAIN, PupilState::UNCERTAIN);
    }
    
    // 获取眼角点和瞳孔点
    cv::Point2f right_inner_corner = (*current_eye_keypoints_)[0];    // 右眼内眼角
    cv::Point2f right_outer_corner = (*current_eye_keypoints_)[4];    // 右眼外眼角
    cv::Point2f right_pupil = (*current_eye_keypoints_)[16];          // 右眼瞳孔
    
    cv::Point2f left_inner_corner = (*current_eye_keypoints_)[17];    // 左眼内眼角
    cv::Point2f left_outer_corner = (*current_eye_keypoints_)[21];    // 左眼外眼角
    cv::Point2f left_pupil = (*current_eye_keypoints_)[33];           // 左眼瞳孔
    
    // 计算眼睛宽度，用于归一化
    float left_eye_width = cv::norm(left_outer_corner - left_inner_corner);
    float right_eye_width = cv::norm(right_outer_corner - right_inner_corner);
    
    // 眼睛宽度合理性检查
    static constexpr float MIN_REASONABLE_EYE_WIDTH = 20.0f;
    static constexpr float MAX_REASONABLE_EYE_WIDTH = 80.0f;
    
    if (left_eye_width < MIN_REASONABLE_EYE_WIDTH || left_eye_width > MAX_REASONABLE_EYE_WIDTH) {
        TX_LOG_WARN("Distraction", "Left eye width seems unreasonable: %.2f (reasonable range: %.1f-%.1f)", 
                   left_eye_width, MIN_REASONABLE_EYE_WIDTH, MAX_REASONABLE_EYE_WIDTH);
    }
    
    if (right_eye_width < MIN_REASONABLE_EYE_WIDTH || right_eye_width > MAX_REASONABLE_EYE_WIDTH) {
        TX_LOG_WARN("Distraction", "Right eye width seems unreasonable: %.2f (reasonable range: %.1f-%.1f)", 
                   right_eye_width, MIN_REASONABLE_EYE_WIDTH, MAX_REASONABLE_EYE_WIDTH);
    }
    
    // 处理左眼的roll矫正和局部坐标系
    float left_eye_dx = left_outer_corner.x - left_inner_corner.x;
    float left_eye_dy = left_outer_corner.y - left_inner_corner.y;
    float left_eye_roll_degrees = atan2f(left_eye_dy, left_eye_dx) * 180.0f / M_PI;
    
    cv::Point2f left_center_point = (left_inner_corner + left_outer_corner) * 0.5f;
    cv::Mat left_rot_mat = cv::getRotationMatrix2D(left_center_point, -left_eye_roll_degrees, 1.0);
    
    std::vector<cv::Point2f> left_all_points = {left_pupil, left_inner_corner, left_outer_corner};
    std::vector<cv::Point2f> left_corrected_points;
    cv::transform(left_all_points, left_corrected_points, left_rot_mat);
    
    cv::Point2f left_pupil_corrected = left_corrected_points[0];
    cv::Point2f left_inner_corrected = left_corrected_points[1];
    cv::Point2f left_outer_corrected = left_corrected_points[2];
    
    float left_origin_x = std::min(left_inner_corrected.x, left_outer_corrected.x);
    float left_origin_y = (left_inner_corrected.y + left_outer_corrected.y) / 2.0f;
    
    float left_pupil_final_x = left_pupil_corrected.x - left_origin_x;
    float left_pupil_final_y = left_pupil_corrected.y - left_origin_y;
    
    float left_normalize_factor = left_eye_width / 2.0f;
    if (left_normalize_factor < 1.0f) {
        TX_LOG_WARN("Distraction", "Left eye normalize factor too small: %.2f, using minimum value 1.0", 
                   left_normalize_factor);
        left_normalize_factor = 1.0f;
    }
    
    float left_pupil_norm_x = left_pupil_final_x / left_normalize_factor;
    float left_pupil_norm_y = left_pupil_final_y / left_normalize_factor;
    
    // 处理右眼的roll矫正和局部坐标系
    float right_eye_dx = right_outer_corner.x - right_inner_corner.x;
    float right_eye_dy = right_outer_corner.y - right_inner_corner.y;
    float right_eye_roll_degrees = atan2f(right_eye_dy, right_eye_dx) * 180.0f / M_PI;
    
    cv::Point2f right_center_point = (right_inner_corner + right_outer_corner) * 0.5f;
    cv::Mat right_rot_mat = cv::getRotationMatrix2D(right_center_point, -right_eye_roll_degrees, 1.0);
    
    std::vector<cv::Point2f> right_all_points = {right_pupil, right_inner_corner, right_outer_corner};
    std::vector<cv::Point2f> right_corrected_points;
    cv::transform(right_all_points, right_corrected_points, right_rot_mat);
    
    cv::Point2f right_pupil_corrected = right_corrected_points[0];
    cv::Point2f right_inner_corrected = right_corrected_points[1];
    cv::Point2f right_outer_corrected = right_corrected_points[2];
    
    float right_origin_x = std::min(right_inner_corrected.x, right_outer_corrected.x);
    float right_origin_y = (right_inner_corrected.y + right_outer_corrected.y) / 2.0f;
    
    float right_pupil_final_x = right_pupil_corrected.x - right_origin_x;
    float right_pupil_final_y = right_pupil_corrected.y - right_origin_y;
    
    float right_normalize_factor = right_eye_width / 2.0f;
    if (right_normalize_factor < 1.0f) {
        TX_LOG_WARN("Distraction", "Right eye normalize factor too small: %.2f, using minimum value 1.0", 
                   right_normalize_factor);
        right_normalize_factor = 1.0f;
    }
    
    float right_pupil_norm_x = right_pupil_final_x / right_normalize_factor;
    float right_pupil_norm_y = right_pupil_final_y / right_normalize_factor;
    
    // 判断左眼瞳孔状态 - 基于三层区域判断
    PupilState left_pupil_state = PupilState::UNCERTAIN;
    if (left_pupil_norm_x >= normal_x_min && left_pupil_norm_x <= normal_x_max &&
        left_pupil_norm_y >= normal_y_min && left_pupil_norm_y <= normal_y_max) {
        // 在正常范围内
        left_pupil_state = PupilState::NORMAL;
    } else if (left_pupil_norm_x >= safe_x_min && left_pupil_norm_x <= safe_x_max &&
               left_pupil_norm_y >= safe_y_min && left_pupil_norm_y <= safe_y_max) {
        // 在安全距离范围内但超出正常范围 - 其它类型
        left_pupil_state = PupilState::UNCERTAIN;
    } else {
        // 超出安全距离范围 - 分心
        left_pupil_state = PupilState::DISTRACTED;
    }
    
    // 判断右眼瞳孔状态 - 基于三层区域判断
    PupilState right_pupil_state = PupilState::UNCERTAIN;
    if (right_pupil_norm_x >= normal_x_min && right_pupil_norm_x <= normal_x_max &&
        right_pupil_norm_y >= normal_y_min && right_pupil_norm_y <= normal_y_max) {
        // 在正常范围内
        right_pupil_state = PupilState::NORMAL;
    } else if (right_pupil_norm_x >= safe_x_min && right_pupil_norm_x <= safe_x_max &&
               right_pupil_norm_y >= safe_y_min && right_pupil_norm_y <= safe_y_max) {
        // 在安全距离范围内但超出正常范围 - 其它类型
        right_pupil_state = PupilState::UNCERTAIN;
    } else {
        // 超出安全距离范围 - 分心
        right_pupil_state = PupilState::DISTRACTED;
    }

    // 对非融合眼进行特殊处理
    const float position_offset = 0.0f; // 位置关系的容差值，可调整
    if (fusion_use_eye == FUSE_EYE_RIGHT) {
        // 右眼是融合眼，左眼是非融合眼
        if (left_pupil_state == PupilState::NORMAL || left_pupil_state == PupilState::UNCERTAIN) {
            if (left_pupil_final_x < (right_pupil_final_x + position_offset)) {
                left_pupil_state = PupilState::NORMAL;
            } else {
                left_pupil_state = PupilState::DISTRACTED;
            }
        }
    } else if (fusion_use_eye == FUSE_EYE_LEFT) {
        // 左眼是融合眼，右眼是非融合眼
        if (right_pupil_state == PupilState::NORMAL || right_pupil_state == PupilState::UNCERTAIN) {
            if (right_pupil_final_x < (left_pupil_final_x + position_offset)) {
                right_pupil_state = PupilState::NORMAL;
            } else {
                right_pupil_state = PupilState::DISTRACTED;
            }
        }
    }
    
    // 返回左右眼的独立状态
    EyesPupilState result(left_pupil_state, right_pupil_state);
    
    // 输出详细信息
    // const char* state_names[] = {"NORMAL", "UNCERTAIN", "DISTRACTED"};
    // std::cout << "Pupil state check - Normal range: x[" << normal_x_min << "," << normal_x_max << "], y[" << normal_y_min << "," << normal_y_max << "]" << "\n"
    // << ", Safety range: x[" << safe_x_min << "," << safe_x_max << "], y[" << safe_y_min << "," << safe_y_max << "] (margins: left-" << safety_margin_left << ", right+" << safety_margin_right << ", up+" << safety_margin_up << ", down-" << safety_margin_down << ")" << "\n"
    // << ", Left eye: raw(" << left_pupil_final_x << ", " << left_pupil_final_y << ") norm(" << left_pupil_norm_x << ", " << left_pupil_norm_y 
    // << ") width:" << left_eye_width << " roll:" << left_eye_roll_degrees << "° state: " << state_names[static_cast<int>(left_pupil_state)] << "\n"
    // << ", Right eye: raw(" << right_pupil_final_x << ", " << right_pupil_final_y << ") norm(" << right_pupil_norm_x << ", " << right_pupil_norm_y 
    // << ") width:" << right_eye_width << " roll:" << right_eye_roll_degrees << "° state: " << state_names[static_cast<int>(right_pupil_state)] << "\n"
    // << ", fusion_use_eye: " << fusion_use_eye << ", Left Result: " << state_names[static_cast<int>(result.left_eye_state)] 
    // << ", Right Result: " << state_names[static_cast<int>(result.right_eye_state)] << std::endl;
    
    return result;
}

}  // namespace tongxing
