# 标定日志系统使用指南

## 概述

新的标定日志系统（CalibrationLogger）采用模块化设计，实现了与核心标定逻辑的解耦，并提供"少而精"的信息展示策略。

## 主要特性

### 1. 模块化设计
- **独立的日志类**：CalibrationLogger与核心标定逻辑完全解耦
- **观察者模式**：支持多种输出方式（控制台、文件等）
- **单例模式**：全局统一的日志管理

### 2. "少而精"的信息展示
- **事件收集**：在时间间隔内收集所有标定事件
- **汇总输出**：定时输出汇总信息，避免频繁打印
- **智能统计**：自动计算进度百分比、预计完成时间、主要过滤原因

### 3. 清晰的信息格式
```
[标定摘要]: 进行中 | 进度: 头部[45%] 左眼[38%] 右眼[42%] | 帧: 120/150 | 预计: 2分30秒 | 主要过滤: 车速过低 (15次)
```

## 核心组件

### CalibrationLogger（日志管理器）
- `recordProgress()`: 记录标定进度
- `recordFilter()`: 记录过滤事件
- `recordSuccess()`: 记录标定成功
- `recordFailure()`: 记录标定失败
- `recordVehicleInfo()`: 记录车辆信息

### CalibrationLogObserver（观察者接口）
- `onSummaryLog()`: 接收汇总日志信息

### ConsoleLogObserver（控制台输出观察者）
- 实现清晰的控制台输出格式

## 使用方法

### 1. 初始化日志系统
```cpp
void DistractionWarn::initCalibrationLogger() {
    auto& logger = CalibrationLogger::getInstance();
    logger.setOutputInterval(CALIBRATION_DEBUG_INTERVAL_MS);
    logger.setEnabled(log_switch);
    
    // 添加控制台观察者
    auto console_observer = std::make_shared<ConsoleLogObserver>();
    logger.addObserver(console_observer);
    
    // 重置统计信息
    logger.resetStats();
    logger.setPhase(CalibrationPhase::PREPARING);
}
```

### 2. 记录标定事件
```cpp
// 记录进度
logger.recordProgress(head_buffer_size, left_eye_buffer_size, right_eye_buffer_size);

// 记录过滤事件
logger.recordFilter("车速过低", "车速15km/h低于20km/h");

// 记录标定成功
logger.recordSuccess(true, true, true);

// 记录标定失败
logger.recordFailure("标定未完成，需要: 头部+右眼");
```

### 3. 自定义观察者
```cpp
class CustomLogObserver : public CalibrationLogObserver {
public:
    void onSummaryLog(const CalibrationStats& stats, CalibrationPhase phase) override {
        // 自定义处理逻辑
        saveToFile(stats, phase);
    }
};

// 添加自定义观察者
auto custom_observer = std::make_shared<CustomLogObserver>();
logger.addObserver(custom_observer);
```

## 输出示例

### 标定准备阶段
```
[标定摘要]: 准备中 | 进度: 头部[0%] 左眼[0%] 右眼[0%] | 帧: 0/10 | 主要过滤: 车速过低 (8次)
```

### 标定进行中
```
[标定摘要]: 进行中 | 进度: 头部[45%] 左眼[38%] 右眼[42%] | 帧: 120/150 | 预计: 2分30秒 | 主要过滤: 人脸角度超范围 (5次)
```

### 标定完成
```
[标定摘要]: 已完成 | 进度: 头部[100%] 左眼[95%] 右眼[97%] | 帧: 285/300
```

### 标定失败
```
[标定摘要]: 失败 | 进度: 头部[100%] 左眼[80%] 右眼[85%] | 帧: 250/300 | 主要过滤: 眼部置信度低 (20次)
```

## 配置选项

### 输出间隔
```cpp
logger.setOutputInterval(1000);  // 1秒输出一次
```

### 启用/禁用
```cpp
logger.setEnabled(true);   // 启用日志
logger.setEnabled(false);  // 禁用日志
```

### 强制输出
```cpp
logger.flushStats();  // 立即输出当前统计信息
```

## 与旧系统的对比

### 旧系统（直接std::cout）
```cpp
std::cout << "[Calibration Debug]: DOING(头部:45/300|左眼:38/300|右眼:42/300)|车速:25|档位:1" << std::endl;
std::cout << "[Calibration Debug]: CONDITION_UNDONE|原因:车速过低(18km/h)" << std::endl;
```

### 新系统（CalibrationLogger）
```cpp
logger.recordProgress(45, 38, 42);
logger.recordFilter("车速过低", "车速18km/h低于20km/h");
// 自动汇总输出：[标定摘要]: 进行中 | 进度: 头部[15%] 左眼[13%] 右眼[14%] | 帧: 120/150 | 主要过滤: 车速过低 (15次)
```

## 优势

### 1. 模块化
- 日志逻辑与业务逻辑分离
- 易于测试和维护
- 支持多种输出方式

### 2. 信息质量
- 汇总显示，避免信息过载
- 智能统计，提供有价值的信息
- 清晰格式，易于理解

### 3. 性能优化
- 减少频繁的字符串操作
- 批量输出，降低I/O开销
- 可配置的输出频率

### 4. 扩展性
- 观察者模式，易于添加新的输出方式
- 统计信息结构化，便于后续分析
- 支持自定义过滤和格式化

## 测试

运行测试程序验证日志系统：
```bash
./test_calibration_logger
```

测试程序会模拟完整的标定过程，展示各种日志输出效果。

## 注意事项

1. **线程安全**：当前实现不是线程安全的，如需在多线程环境使用，需要添加互斥锁
2. **内存管理**：观察者使用shared_ptr管理，注意避免循环引用
3. **性能考虑**：虽然已优化，但在高频调用场景下仍需注意性能影响
4. **向后兼容**：保留了原有的log_switch控制逻辑，确保向后兼容
