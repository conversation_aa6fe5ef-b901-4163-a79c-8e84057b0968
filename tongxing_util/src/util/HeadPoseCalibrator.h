#ifndef HEAD_POSE_CALIBRATOR_H
#define HEAD_POSE_CALIBRATOR_H

#include <algorithm>
#include <cmath>
#include <deque>
#include <iostream>
#include <opencv2/opencv.hpp>
#include <vector>

typedef struct calistatus_ {
    bool head_cali_finish;
    bool leye_cali_finish;
    bool reye_cali_finish;
} calistatus;

namespace tongxing {

class HeadPoseCalibrator {
  public:
    HeadPoseCalibrator(int queue_size = 100,
                       int queue_start_frame = 50,
                       int k_num = 2,
                       float cluster_radius = 6.0,
                       float threshold_50f = 0.7,
                       float threshold_100f = 0.8,
                       float threshold_longer = 0.9,
                       float eye_cluster_radius = 6.0,
                       float eye_threshold_50f = 0.7,
                       float eye_threshold_100f = 0.8,
                       float eye_threshold_longerf = 0.9);
    void init();
    void addNewPoint(const std::vector<float>& new_point,
                     std::deque<std::vector<float>>& data_deque_,
                     std::vector<std::vector<float>>& distance_matrix);
    bool execute(std::vector<float>& head_angles,
                 std::vector<float>& lefteye_angles,
                 std::vector<float>& righteye_angles,
                 const float leye_uper_curve_score,
                 const float reye_uper_curve_score,
                 std::vector<float>& centroid_result,
                 std::vector<float>& leye_centroid_result,
                 std::vector<float>& reye_centroid_result,
                 float& leye_uper_curve_score_mean,
                 float& reye_uper_curve_score_mean,
                 calistatus& status);
    bool aggregationCheck(std::deque<std::vector<float>>& data_deque_,
                          std::vector<std::vector<float>>& dis_matrix,
                          float cluster_radius_,
                          float threshold,
                          std::pair<size_t, std::vector<size_t>>& founded_index);
    void clear();
    bool isUpdateReasonable(std::vector<float>& last_eye_centroid, 
                                                    std::vector<float>& eye_centroid);
    bool areAnglesReasonable(const std::vector<float>& angles);
    void quickFilling(std::vector<float>& head_angles,
                              std::vector<float>& lefteye_angles,
                              std::vector<float>& righteye_angles,
                              const float leye_uper_curve_score,
                              const float reye_uper_curve_score);
    void changeparam(float cluster_radius);
    
    // 设置时间长度挡位的接口函数
    void setTimeFrameThresholds(size_t frames_lvl1, size_t frames_lvl2, size_t frames_lvl3);

    // 获取缓冲区大小的接口函数(用于调试)
    int getHeadBufferSize() const;
    int getLeftEyeBufferSize() const;
    int getRightEyeBufferSize() const;

  private:
    float calculateDynamicThreshold(size_t frame_count, float threshold_50, float threshold_100, float threshold_longer);
    int headqueue_size_;
    int queue_start_frame_;
    int k_num_;
    float cluster_radius_;
    float threshold_50f_;
    float threshold_100f_;
    float threshold_longerf_;
    float eye_cluster_radius_;
    float eye_threshold_50f_;
    float eye_threshold_100f_;
    float eye_threshold_longerf_;

    // 时间长度挡位成员变量
    size_t frames_lvl1_;    // 第一挡位时间长度，默认100帧
    size_t frames_lvl2_;    // 第二挡位时间长度，默认200帧
    size_t frames_lvl3_;    // 第三挡位时间长度，默认300帧

    int process_counter_;
    bool head_cali_ok_;
    bool leye_cali_ok_;
    bool reye_cali_ok_;
    bool global_cali_finish;
    std::vector<std::vector<float>> headdis_matrix;
    std::vector<std::vector<float>> leyedis_matrix;
    std::vector<std::vector<float>> reyedis_matrix;

    std::deque<std::vector<float>> headdata_deque_;
    std::deque<std::vector<float>> leyedata_deque_;
    std::deque<std::vector<float>> reyedata_deque_;

    std::vector<float> thelast_head_centroid;
    std::vector<float> thelast_leye_centroid;
    std::vector<float> thelast_reye_centroid;
    std::deque<float> leye_uper_curve_data;
    std::deque<float> reye_uper_curve_data;

    static constexpr float KMinFrontCurveScore = 0.0020f;
    static constexpr float KMaxFrontCurveScore = 0.0035f;
    static constexpr float KMaxEyeDifferY = 0.36f; // 在图像坐标系下，对应的一个像素的偏差 (纠正：外面接口传进来的顺序不是常规的顺序以及比例也不是常规的比例。。。。)
    static constexpr float KMaxEyeDifferX = 1.2f;
    static constexpr float KMaxHeadUpdateDistance = 5.0f;
    static constexpr float KMinReasonbleAngle = -50.0f;
    static constexpr float KMaxReasonbleAngle = 50.0f;
  };
}  // namespace tongxing
#endif  // HEAD_POSE_CALIBRATOR_H
