#include "calibration_debug.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <algorithm>
#include <json/json.h>
#include <fstream>

namespace tongxing {

// 静态成员初始化
CalibrationDebugger* CalibrationDebugger::instance_ = nullptr;
std::mutex CalibrationDebugger::instance_mutex_;

CalibrationDebugger* CalibrationDebugger::getInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex_);
    if (instance_ == nullptr) {
        instance_ = new CalibrationDebugger();
    }
    return instance_;
}

void CalibrationDebugger::initialize(const CalibrationDebugConfig& config) {
    std::lock_guard<std::mutex> lock(records_mutex_);
    config_ = config;
    
    if (config_.save_to_file && !log_file_.is_open()) {
        log_file_.open(config_.log_file_path, std::ios::out | std::ios::app);
        if (log_file_.is_open()) {
            writeToFile("=== 标定调试会话开始 ===\n");
        }
    }
    
    // 重置统计信息
    stats_ = CalibrationProgressStats();
    records_.clear();
}

void CalibrationDebugger::loadConfigFromJson(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cout << "CalibrationDebugger: 无法打开配置文件 " << config_file << std::endl;
        return;
    }
    
    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(file, root)) {
        std::cout << "CalibrationDebugger: 配置文件解析失败" << std::endl;
        return;
    }
    
    if (root.isMember("calibration_debug")) {
        Json::Value debug_config = root["calibration_debug"];
        CalibrationDebugConfig config;
        
        config.enable = debug_config.get("enable", false).asBool();
        config.log_level = debug_config.get("log_level", "summary").asString();
        config.output_interval = debug_config.get("output_interval", 100).asInt();
        config.save_to_file = debug_config.get("save_to_file", false).asBool();
        config.log_file_path = debug_config.get("log_file_path", "./calibration_debug.log").asString();
        config.max_records = debug_config.get("max_records", 1000).asInt();
        
        initialize(config);
        
        if (config_.enable) {
            std::cout << "CalibrationDebugger: 调试功能已启用, 日志级别=" << config_.log_level 
                      << ", 输出间隔=" << config_.output_interval << "帧" << std::endl;
        }
    }
}

void CalibrationDebugger::recordFrame(const CalibrationDebugRecord& record) {
    if (!config_.enable) return;
    
    std::lock_guard<std::mutex> lock(records_mutex_);
    
    // 更新统计信息
    stats_.total_frames_processed++;
    if (record.filter_reason == CalibrationFilterReason::ACCEPTED) {
        stats_.frames_accepted++;
    }
    updateFilterReasonCounts(record.filter_reason);
    
    // 添加到环形缓冲区
    records_.push_back(record);
    if (records_.size() > static_cast<size_t>(config_.max_records)) {
        records_.pop_front();
    }
    
    // 定期输出进度
    if (stats_.total_frames_processed % config_.output_interval == 0) {
        printRealTimeProgress();
    }
    
    // 写入文件
    if (config_.save_to_file && config_.log_level == "detailed") {
        std::stringstream ss;
        ss << "Frame " << stats_.total_frames_processed 
           << " [" << filterReasonToString(record.filter_reason) << "] "
           << record.detailed_reason << "\n";
        writeToFile(ss.str());
    }
}

void CalibrationDebugger::updateProgress(int head_buffer_size, int left_eye_buffer_size, int right_eye_buffer_size,
                                       bool head_calibrated, bool left_eye_calibrated, bool right_eye_calibrated) {
    if (!config_.enable) return;
    
    std::lock_guard<std::mutex> lock(records_mutex_);
    stats_.head_current_frames = head_buffer_size;
    stats_.left_eye_current_frames = left_eye_buffer_size;
    stats_.right_eye_current_frames = right_eye_buffer_size;
    stats_.head_calibration_finished = head_calibrated;
    stats_.left_eye_calibration_finished = left_eye_calibrated;
    stats_.right_eye_calibration_finished = right_eye_calibrated;
}

void CalibrationDebugger::printRealTimeProgress() {
    if (!config_.enable || config_.log_level == "none") return;
    
    std::string report = generateProgressReport();
    std::cout << report << std::endl;
    
    if (config_.save_to_file) {
        writeToFile(report + "\n");
    }
}

std::string CalibrationDebugger::generateProgressReport() {
    std::lock_guard<std::mutex> lock(records_mutex_);
    std::stringstream ss;
    
    ss << "\n=== 标定进度报告 (Frame: " << stats_.total_frames_processed << ") ===\n";
    ss << "总处理帧数: " << stats_.total_frames_processed << "\n";
    
    if (stats_.total_frames_processed > 0) {
        double accept_rate = (double)stats_.frames_accepted / stats_.total_frames_processed * 100.0;
        ss << "数据接受率: " << std::fixed << std::setprecision(1) << accept_rate 
           << "% (" << stats_.frames_accepted << "/" << stats_.total_frames_processed << ")\n\n";
    }
    
    // 各部分标定进度
    ss << "头部标定: " << generateProgressBar(stats_.head_current_frames, stats_.head_target_frames) 
       << " " << std::fixed << std::setprecision(0) 
       << (double)stats_.head_current_frames / stats_.head_target_frames * 100 << "% "
       << "(" << stats_.head_current_frames << "/" << stats_.head_target_frames << "帧)";
    if (stats_.head_calibration_finished) ss << " ✓";
    ss << "\n";
    
    ss << "左眼标定: " << generateProgressBar(stats_.left_eye_current_frames, stats_.left_eye_target_frames)
       << " " << std::fixed << std::setprecision(0)
       << (double)stats_.left_eye_current_frames / stats_.left_eye_target_frames * 100 << "% "
       << "(" << stats_.left_eye_current_frames << "/" << stats_.left_eye_target_frames << "帧)";
    if (stats_.left_eye_calibration_finished) ss << " ✓";
    ss << "\n";
    
    ss << "右眼标定: " << generateProgressBar(stats_.right_eye_current_frames, stats_.right_eye_target_frames)
       << " " << std::fixed << std::setprecision(0)
       << (double)stats_.right_eye_current_frames / stats_.right_eye_target_frames * 100 << "% "
       << "(" << stats_.right_eye_current_frames << "/" << stats_.right_eye_target_frames << "帧)";
    if (stats_.right_eye_calibration_finished) ss << " ✓";
    ss << "\n\n";
    
    // 过滤原因统计(只显示前5个)
    if (!stats_.filter_reason_counts.empty()) {
        ss << "数据被过滤原因统计:\n";
        
        // 按数量排序
        std::vector<std::pair<CalibrationFilterReason, int>> sorted_reasons;
        for (const auto& pair : stats_.filter_reason_counts) {
            if (pair.first != CalibrationFilterReason::ACCEPTED) {
                sorted_reasons.push_back(pair);
            }
        }
        std::sort(sorted_reasons.begin(), sorted_reasons.end(), 
                 [](const auto& a, const auto& b) { return a.second > b.second; });
        
        int count = 0;
        for (const auto& pair : sorted_reasons) {
            if (count >= 5) break;
            double percentage = (double)pair.second / stats_.total_frames_processed * 100.0;
            ss << "- " << filterReasonToString(pair.first) << ": " << pair.second << "帧 ("
               << std::fixed << std::setprecision(1) << percentage << "%)\n";
            count++;
        }
    }
    
    // 当前标定状态
    ss << "\n当前标定状态: ";
    if (stats_.head_calibration_finished && stats_.left_eye_calibration_finished && stats_.right_eye_calibration_finished) {
        ss << "已完成";
    } else {
        ss << "进行中";
        int remaining = std::max({stats_.head_target_frames - stats_.head_current_frames,
                                 stats_.left_eye_target_frames - stats_.left_eye_current_frames,
                                 stats_.right_eye_target_frames - stats_.right_eye_current_frames});
        if (remaining > 0) {
            ss << " (约需再处理" << remaining << "帧)";
        }
    }
    ss << "\n";
    
    return ss.str();
}

std::string CalibrationDebugger::filterReasonToString(CalibrationFilterReason reason) {
    switch (reason) {
        case CalibrationFilterReason::ACCEPTED: return "数据接受";
        case CalibrationFilterReason::VEHICLE_SPEED_LOW: return "车速过低";
        case CalibrationFilterReason::VEHICLE_GEAR_INVALID: return "档位无效";
        case CalibrationFilterReason::HEAD_YAW_OUT_RANGE: return "头部yaw角超范围";
        case CalibrationFilterReason::HEAD_PITCH_OUT_RANGE: return "头部pitch角超范围";
        case CalibrationFilterReason::HEAD_ROLL_OUT_RANGE: return "头部roll角超范围";
        case CalibrationFilterReason::STEERING_ANGLE_INVALID: return "方向盘角度无效";
        case CalibrationFilterReason::LEFT_EYE_CONF_LOW: return "左眼置信度低";
        case CalibrationFilterReason::RIGHT_EYE_CONF_LOW: return "右眼置信度低";
        case CalibrationFilterReason::LEFT_PUPIL_POSITION_INVALID: return "左眼瞳孔位置无效";
        case CalibrationFilterReason::RIGHT_PUPIL_POSITION_INVALID: return "右眼瞳孔位置无效";
        case CalibrationFilterReason::EYE_GEOMETRY_INVALID: return "眼部几何约束不满足";
        case CalibrationFilterReason::HEAD_ANGLE_OUT_REASONABLE_RANGE: return "头部角度超出合理范围";
        case CalibrationFilterReason::LEFT_EYE_ANGLE_OUT_REASONABLE_RANGE: return "左眼角度超出合理范围";
        case CalibrationFilterReason::RIGHT_EYE_ANGLE_OUT_REASONABLE_RANGE: return "右眼角度超出合理范围";
        case CalibrationFilterReason::BUFFER_INSUFFICIENT: return "缓冲区数据不足";
        case CalibrationFilterReason::PROCESS_FREQUENCY_LIMIT: return "处理频率限制";
        case CalibrationFilterReason::HEAD_UPDATE_DISTANCE_LARGE: return "头部更新距离过大";
        case CalibrationFilterReason::LEFT_EYE_UPDATE_UNREASONABLE: return "左眼更新不合理";
        case CalibrationFilterReason::RIGHT_EYE_UPDATE_UNREASONABLE: return "右眼更新不合理";
        default: return "未知原因";
    }
}

std::string CalibrationDebugger::generateProgressBar(int current, int total, int width) {
    if (total <= 0) return std::string(width, '░');
    
    int filled = std::min(width, (int)((double)current / total * width));
    std::string bar = std::string(filled, '█') + std::string(width - filled, '░');
    return "[" + bar + "]";
}

void CalibrationDebugger::updateFilterReasonCounts(CalibrationFilterReason reason) {
    stats_.filter_reason_counts[reason]++;
}

void CalibrationDebugger::writeToFile(const std::string& content) {
    if (log_file_.is_open()) {
        log_file_ << content;
        log_file_.flush();
    }
}

void CalibrationDebugger::reset() {
    std::lock_guard<std::mutex> lock(records_mutex_);
    stats_ = CalibrationProgressStats();
    records_.clear();
    
    if (config_.save_to_file && log_file_.is_open()) {
        writeToFile("=== 标定调试重置 ===\n");
    }
}

void CalibrationDebugger::cleanup() {
    std::lock_guard<std::mutex> lock(records_mutex_);
    if (log_file_.is_open()) {
        writeToFile("=== 标定调试会话结束 ===\n");
        log_file_.close();
    }
}

} // namespace tongxing
