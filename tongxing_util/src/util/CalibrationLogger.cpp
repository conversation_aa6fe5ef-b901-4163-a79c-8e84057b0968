#include "CalibrationLogger.h"
#include <iostream>
#include <algorithm>
#include <iomanip>
#include <sstream>

namespace tongxing {

CalibrationLogger& CalibrationLogger::getInstance() {
    static CalibrationLogger instance;
    return instance;
}

CalibrationLogger::CalibrationLogger() 
    : output_interval_ms_(1000), enabled_(true), current_phase_(CalibrationPhase::PREPARING) {
    last_output_time_ = std::chrono::system_clock::now();
}

void CalibrationLogger::addObserver(std::shared_ptr<CalibrationLogObserver> observer) {
    observers_.push_back(observer);
}

void CalibrationLogger::removeObserver(std::shared_ptr<CalibrationLogObserver> observer) {
    observers_.erase(
        std::remove_if(observers_.begin(), observers_.end(),
            [&](const std::shared_ptr<CalibrationLogObserver>& o) {
                return o == observer;
            }),
        observers_.end());
}

void CalibrationLogger::recordProgress(int head_buffer, int left_eye_buffer, int right_eye_buffer) {
    if (!enabled_) return;
    
    current_stats_.total_frames++;
    current_stats_.accepted_frames++;
    
    // 更新缓冲区大小
    current_stats_.head_progress_percent = (head_buffer * 100) / TARGET_BUFFER_SIZE;
    current_stats_.left_eye_progress_percent = (left_eye_buffer * 100) / TARGET_BUFFER_SIZE;
    current_stats_.right_eye_progress_percent = (right_eye_buffer * 100) / TARGET_BUFFER_SIZE;
    
    // 计算预计完成时间
    calculateEstimatedTime();
    
    // 检查是否应该输出汇总信息
    checkAndOutputSummary();
}

void CalibrationLogger::recordFilter(const std::string& reason, const std::string& details) {
    if (!enabled_) return;
    
    current_stats_.total_frames++;
    current_stats_.filtered_frames++;
    
    // 记录过滤原因
    if (current_stats_.filter_reasons.find(reason) != current_stats_.filter_reasons.end()) {
        current_stats_.filter_reasons[reason]++;
    } else {
        current_stats_.filter_reasons[reason] = 1;
    }
    
    // 检查是否应该输出汇总信息
    checkAndOutputSummary();
}

void CalibrationLogger::recordSuccess(bool head_complete, bool left_eye_complete, bool right_eye_complete) {
    if (!enabled_) return;
    
    // 更新阶段
    current_phase_ = CalibrationPhase::COMPLETED;
    
    // 强制输出汇总信息
    flushStats();
}

void CalibrationLogger::recordFailure(const std::string& reason) {
    if (!enabled_) return;
    
    // 更新阶段
    current_phase_ = CalibrationPhase::FAILED;
    
    // 强制输出汇总信息
    flushStats();
}

void CalibrationLogger::recordVehicleInfo(float speed, int gear) {
    if (!enabled_) return;
    
    last_vehicle_speed_ = speed;
    last_vehicle_gear_ = gear;
}

void CalibrationLogger::setOutputInterval(long interval_ms) {
    output_interval_ms_ = interval_ms;
}

void CalibrationLogger::setEnabled(bool enabled) {
    enabled_ = enabled;
}

void CalibrationLogger::flushStats() {
    if (!enabled_) return;
    
    // 更新进度百分比
    updateProgressPercent();
    
    // 通知所有观察者
    notifyObservers();
    
    // 更新最后输出时间
    last_output_time_ = std::chrono::system_clock::now();
}

void CalibrationLogger::resetStats() {
    current_stats_ = CalibrationStats();
    current_phase_ = CalibrationPhase::PREPARING;
}

void CalibrationLogger::setPhase(CalibrationPhase phase) {
    current_phase_ = phase;
}

void CalibrationLogger::checkAndOutputSummary() {
    auto now = std::chrono::system_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
        now - last_output_time_).count();
    
    if (elapsed >= output_interval_ms_) {
        // 更新进度百分比
        updateProgressPercent();
        
        // 通知所有观察者
        notifyObservers();
        
        // 更新最后输出时间
        last_output_time_ = now;
    }
}

void CalibrationLogger::notifyObservers() {
    for (const auto& observer : observers_) {
        observer->onSummaryLog(current_stats_, current_phase_);
    }
}

void CalibrationLogger::calculateEstimatedTime() {
    // 找出进度最低的缓冲区
    int min_progress = std::min({
        current_stats_.head_progress_percent,
        current_stats_.left_eye_progress_percent,
        current_stats_.right_eye_progress_percent
    });
    
    // 如果进度为0或100%，则无法估计
    if (min_progress <= 0 || min_progress >= 100) {
        current_stats_.estimated_seconds_remaining = -1;
        return;
    }
    
    // 计算已经过去的时间
    auto now = std::chrono::system_clock::now();
    auto elapsed_seconds = std::chrono::duration_cast<std::chrono::seconds>(
        now - current_stats_.start_time).count();
    
    // 根据当前进度估计剩余时间
    if (elapsed_seconds > 0) {
        double seconds_per_percent = static_cast<double>(elapsed_seconds) / min_progress;
        int remaining_percent = 100 - min_progress;
        current_stats_.estimated_seconds_remaining = static_cast<int>(seconds_per_percent * remaining_percent);
    } else {
        current_stats_.estimated_seconds_remaining = -1;
    }
}

void CalibrationLogger::updateProgressPercent() {
    // 进度百分比已在recordProgress中更新
}

void ConsoleLogObserver::onSummaryLog(const CalibrationStats& stats, CalibrationPhase phase) {
    std::stringstream ss;
    
    // 标题
    ss << "[标定摘要]: ";
    
    // 阶段
    ss << formatPhase(phase) << " | ";
    
    // 进度
    ss << "进度: 头部" << formatProgress(stats.head_progress_percent)
       << " 左眼" << formatProgress(stats.left_eye_progress_percent)
       << " 右眼" << formatProgress(stats.right_eye_progress_percent) << " | ";
    
    // 帧统计
    ss << "帧: " << stats.accepted_frames << "/" << stats.total_frames;
    
    // 车辆信息
    if (phase != CalibrationPhase::COMPLETED && phase != CalibrationPhase::FAILED) {
        // 预计完成时间
        if (stats.estimated_seconds_remaining > 0) {
            ss << " | 预计: " << formatTime(stats.estimated_seconds_remaining);
        }
    }
    
    // 最严重的过滤原因
    if (!stats.filter_reasons.empty()) {
        // 找出最频繁的过滤原因
        auto max_reason = std::max_element(
            stats.filter_reasons.begin(), stats.filter_reasons.end(),
            [](const auto& a, const auto& b) { return a.second < b.second; }
        );
        
        ss << " | 主要过滤: " << max_reason->first 
           << " (" << max_reason->second << "次)";
    }
    
    std::cout << ss.str() << std::endl;
}

std::string ConsoleLogObserver::formatPhase(CalibrationPhase phase) {
    switch (phase) {
        case CalibrationPhase::PREPARING:
            return "准备中";
        case CalibrationPhase::IN_PROGRESS:
            return "进行中";
        case CalibrationPhase::COMPLETED:
            return "已完成";
        case CalibrationPhase::FAILED:
            return "失败";
        default:
            return "未知";
    }
}

std::string ConsoleLogObserver::formatProgress(int percent) {
    std::stringstream ss;
    ss << "[" << percent << "%]";
    return ss.str();
}

std::string ConsoleLogObserver::formatTime(int seconds) {
    int minutes = seconds / 60;
    seconds %= 60;
    
    std::stringstream ss;
    if (minutes > 0) {
        ss << minutes << "分";
    }
    ss << seconds << "秒";
    
    return ss.str();
}

} // namespace tongxing
