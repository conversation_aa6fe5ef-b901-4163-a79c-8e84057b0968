# 轻量级标定调试系统

## 概述

轻量级标定调试系统为DMS（驾驶员监控系统）的自动标定流程提供了简洁实用的调试信息输出。该系统采用最小侵入性设计，通过时间间隔输出关键的标定状态和过滤原因。

## 功能特性

### 1. 主标定流程调试 (auto_calibration_distraction)
- 每秒输出一次标定关键信息
- 显示标定状态（DOING/DONE/CONDITION_UNDONE）
- 显示车辆状态、头部角度、眼部置信度
- 显示缓冲区进度

### 2. 标定器内部调试 (HeadPoseCalibrator)
- 显示角度合理性检查结果
- 显示缓冲区数据不足情况
- 显示处理频率限制
- 显示更新距离检查
- 显示标定完成状态

### 3. 最小性能影响
- 使用现有的log_switch控制开关
- 简单的字符串输出，无复杂数据结构
- 时间间隔控制，避免频繁输出

## 输出示例

### 主标定流程输出

#### 正常标定进行中
```
[Calibration Debug]: DOING(头部:45/300|左眼:38/300|右眼:42/300)|车速:25|档位:1|头部角度:5.2,3.1,1.8|眼部置信度:1.2,1.1
```

#### 标定完成
```
[Calibration Debug]: DONE|车速:30|档位:1|头部角度:2.1,1.5,0.8|眼部置信度:1.3,1.2
[Calibration Debug]: 标定成功|头部:完成|左眼:完成|右眼:完成|融合眼:右眼
```

#### 标定未完成
```
[Calibration Debug]: DOING(头部:满|左眼:280/300|右眼:290/300)|车速:25|档位:1|头部角度:5.2,3.1,1.8|眼部置信度:1.2,1.1
[Calibration Debug]: 标定未完成|头部:完成|左眼:未完成|右眼:未完成|需要:头部+右眼
```

#### 条件不满足 - 车速过低
```
[Calibration Debug]: CONDITION_UNDONE|原因:车速过低(18km/h)
```

#### 条件不满足 - 人脸角度超范围
```
[Calibration Debug]: CONDITION_UNDONE|原因:人脸角度超范围(yaw:26.5)
```

#### 条件不满足 - 方向盘角度无效
```
[Calibration Debug]: CONDITION_UNDONE|原因:方向盘角度无效(0.8)
```

### 标定器内部调试输出

#### 角度超出合理范围
```
[Calibrator Debug]: 头部角度超出[-50°,50°]范围(55.2,3.1,1.8)
[Calibrator Debug]: 左眼角度超出[-50°,50°]范围(2.1,55.0)
[Calibrator Debug]: 右眼角度超出[-50°,50°]范围(1.8,52.3)
```

#### 缓冲区和频率限制
```
[Calibrator Debug]: 缓冲区数据不足(35<=40)
[Calibrator Debug]: 处理频率限制(未标定完成,5<10)
[Calibrator Debug]: 处理频率限制(已标定完成,45<100)
```

#### 更新距离检查
```
[Calibrator Debug]: 头部更新距离过大(6.2>5.0)
[Calibrator Debug]: 左眼更新变化过大
[Calibrator Debug]: 右眼更新变化过大
```

#### 标定完成
```
[Calibrator Debug]: 标定完成|头部:300|左眼:285|右眼:295
```

## 使用方法

### 1. 启用调试输出
确保在代码中设置了`log_switch = true`，或者在配置文件中启用了日志开关。

### 2. 运行程序
正常运行DMS程序，调试信息会自动输出到控制台。

### 3. 观察输出
- 每秒会输出一次当前的标定状态
- 当条件不满足时，会显示具体的过滤原因
- 通过缓冲区数值可以观察标定进度
- 标定器内部的详细过滤信息也会显示

## 输出字段说明

### 主标定流程字段

#### 标定状态
- `DOING`: 标定正在进行中
- `DONE`: 标定已完成
- `CONDITION_UNDONE`: 标定条件不满足

#### 车辆信息
- `车速`: 当前车速（km/h）
- `档位`: 当前档位（1=前进档，其他值表示非前进档）

#### 头部角度
- 格式：`yaw,pitch,roll`
- 单位：度（°）

#### 眼部置信度
- 格式：`左眼置信度,右眼置信度`
- 范围：通常在0.0-2.0之间，≥1.0表示可用

#### 缓冲区进度
- DOING状态格式：`(头部:45/300|左眼:38/300|右眼:42/300)`
- 数值：当前缓冲区中的数据帧数/目标帧数
- 目标：通常需要达到300帧完成标定
- "满"表示已达到目标帧数

#### 标定成功/失败原因
- 标定成功：显示各部分完成状态和使用的融合眼
- 标定未完成：显示各部分状态和需要完成的条件
- 融合眼类型：左眼/右眼/双眼

### 标定器内部字段

#### 角度范围检查
- 所有角度必须在[-50°, 50°]范围内
- 超出范围的角度会被拒绝

#### 缓冲区检查
- 头部缓冲区需要至少40帧数据才能开始标定判断

#### 处理频率限制
- 未标定完成：每10帧处理一次
- 已标定完成：每100帧重新校正一次

#### 更新距离检查
- 头部更新距离不能超过5.0
- 眼部更新变化需要在合理范围内

## 故障排除

### 1. 没有调试输出
- 检查`log_switch`是否设置为true
- 确认程序正在调用相关的标定方法

### 2. 输出频率过高
- 调试输出已限制为每秒一次
- 如需调整频率，可修改代码中的时间间隔（1000ms）

### 3. 标定一直显示DOING
- 检查车速是否≥20km/h
- 检查档位是否为前进档
- 检查头部角度是否在合理范围内
- 观察缓冲区数值是否在增长
- 查看标定器内部是否有角度超范围的提示

### 4. 缓冲区不增长
- 查看是否有"角度超出[-50°,50°]范围"的提示
- 检查是否有"处理频率限制"的提示
- 确认数据质量是否满足要求

## 开发者信息

### 修改位置
- 主要输出逻辑在`src/warming/dms_distraction_warning_byd.cpp`的`auto_calibration_distraction`方法中
- 标定器内部输出在`tongxing_util/src/util/HeadPoseCalibrator.cpp`的`execute`方法中
- 使用静态变量控制输出频率
- 通过现有的`log_switch`变量控制是否输出

### 自定义调试信息
如需添加更多调试信息，可以在现有的输出语句中添加新的字段：
```cpp
std::cout << "|新字段:" << 新数值;
```

### 性能考虑
- 时间间隔检查的开销极小
- 字符串拼接只在需要输出时进行
- 不影响标定算法的性能

## 版本历史

- v1.0: 轻量级版本，提供基本的标定状态和过滤原因输出
- v1.1: 添加HeadPoseCalibrator内部调试信息，提供更详细的过滤原因
- v1.2: 修正时间戳更新逻辑，添加明确的成功/失败原因显示，改进进度显示格式
