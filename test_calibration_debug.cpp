#include "src/warming/calibration_debug.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace tongxing;

int main() {
    std::cout << "=== 标定调试系统测试 ===" << std::endl;
    
    // 1. 初始化调试器
    CalibrationDebugConfig config;
    config.enable = true;
    config.log_level = "detailed";
    config.output_interval = 10; // 每10帧输出一次进度
    config.save_to_file = true;
    config.log_file_path = "./test_calibration_debug.log";
    config.max_records = 100;
    
    CalibrationDebugger::getInstance()->initialize(config);
    
    std::cout << "调试器初始化完成" << std::endl;
    
    // 2. 模拟各种过滤场景
    CalibrationDebugRecord record;
    record.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    // 模拟车速过低的情况
    for (int i = 1; i <= 5; i++) {
        record.filter_reason = CalibrationFilterReason::VEHICLE_SPEED_LOW;
        record.detailed_reason = "车速" + std::to_string(15 + i) + "km/h低于20km/h";
        record.vehicle_speed = 15 + i;
        CalibrationDebugger::getInstance()->recordFrame(record);
        std::cout << "记录车速过低帧 " << i << std::endl;
    }
    
    // 模拟头部角度超范围的情况
    for (int i = 1; i <= 8; i++) {
        record.filter_reason = CalibrationFilterReason::HEAD_YAW_OUT_RANGE;
        record.detailed_reason = "头部yaw角" + std::to_string(25 + i) + "超出范围[-25,25]";
        record.head_yaw = 25 + i;
        CalibrationDebugger::getInstance()->recordFrame(record);
        std::cout << "记录头部角度超范围帧 " << i << std::endl;
    }

    // 模拟眼部置信度低的情况
    for (int i = 1; i <= 3; i++) {
        record.filter_reason = CalibrationFilterReason::LEFT_EYE_CONF_LOW;
        record.detailed_reason = "左眼置信度" + std::to_string(0.5 + i * 0.1) + "低于1.0";
        record.left_eye_conf = 0.5 + i * 0.1;
        CalibrationDebugger::getInstance()->recordFrame(record);
        std::cout << "记录眼部置信度低帧 " << i << std::endl;
    }
    
    // 模拟数据被接受的情况
    for (int i = 1; i <= 15; i++) {
        record.filter_reason = CalibrationFilterReason::ACCEPTED;
        record.detailed_reason = "数据通过所有过滤条件，进入标定流程";
        record.vehicle_speed = 25;
        record.head_yaw = 5;
        record.head_pitch = 3;
        record.head_roll = 2;
        record.left_eye_conf = 1.2;
        record.right_eye_conf = 1.1;
        
        // 模拟进度更新
        CalibrationDebugger::getInstance()->updateProgress(
            i * 10,  // head_buffer_size
            i * 8,   // left_eye_buffer_size  
            i * 9,   // right_eye_buffer_size
            i >= 12, // head_calibrated
            i >= 14, // left_eye_calibrated
            i >= 13  // right_eye_calibrated
        );
        
        CalibrationDebugger::getInstance()->recordFrame(record);
        std::cout << "记录数据接受帧 " << i << std::endl;
    }
    
    // 3. 生成最终报告
    std::cout << "\n=== 最终调试报告 ===" << std::endl;
    std::string final_report = CalibrationDebugger::getInstance()->generateProgressReport();
    std::cout << final_report << std::endl;
    
    // 4. 清理
    CalibrationDebugger::getInstance()->cleanup();
    
    std::cout << "\n测试完成！请查看 test_calibration_debug.log 文件获取详细日志。" << std::endl;
    
    return 0;
}
