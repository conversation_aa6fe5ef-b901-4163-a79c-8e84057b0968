#ifndef CALIBRATION_DEBUG_H
#define CALIBRATION_DEBUG_H

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <fstream>
#include <mutex>
#include <deque>

namespace tongxing {

// 标定数据被过滤的原因枚举
enum class CalibrationFilterReason {
    ACCEPTED = 0,                    // 数据被接受
    
    // 第一层：车辆状态过滤
    VEHICLE_SPEED_LOW,              // 车速过低
    VEHICLE_GEAR_INVALID,           // 档位无效
    
    // 第二层：人脸角度过滤  
    HEAD_YAW_OUT_RANGE,             // 头部yaw角超范围
    HEAD_PITCH_OUT_RANGE,           // 头部pitch角超范围
    HEAD_ROLL_OUT_RANGE,            // 头部roll角超范围
    STEERING_ANGLE_INVALID,         // 方向盘角度无效
    
    // 第三层：眼部质量过滤
    LEFT_EYE_CONF_LOW,              // 左眼置信度低
    RIGHT_EYE_CONF_LOW,             // 右眼置信度低
    LEFT_PUPIL_POSITION_INVALID,    // 左眼瞳孔位置无效
    RIGHT_PUPIL_POSITION_INVALID,   // 右眼瞳孔位置无效
    EYE_GEOMETRY_INVALID,           // 眼部几何约束不满足
    
    // 第四层：角度合理性过滤
    HEAD_ANGLE_OUT_REASONABLE_RANGE, // 头部角度超出合理范围
    LEFT_EYE_ANGLE_OUT_REASONABLE_RANGE, // 左眼角度超出合理范围
    RIGHT_EYE_ANGLE_OUT_REASONABLE_RANGE, // 右眼角度超出合理范围
    
    // 第五层：时序控制过滤
    BUFFER_INSUFFICIENT,            // 缓冲区数据不足
    PROCESS_FREQUENCY_LIMIT,        // 处理频率限制
    
    // 第六层：变化幅度过滤
    HEAD_UPDATE_DISTANCE_LARGE,     // 头部更新距离过大
    LEFT_EYE_UPDATE_UNREASONABLE,   // 左眼更新不合理
    RIGHT_EYE_UPDATE_UNREASONABLE,  // 右眼更新不合理
    
    // 其他原因
    UNKNOWN_REASON                  // 未知原因
};

// 标定调试记录结构
struct CalibrationDebugRecord {
    long timestamp;                         // 时间戳
    CalibrationFilterReason filter_reason; // 过滤原因
    std::string detailed_reason;            // 详细原因描述
    
    // 原始数据
    float head_yaw, head_pitch, head_roll;
    float left_eye_yaw, left_eye_pitch;
    float right_eye_yaw, right_eye_pitch;
    float left_eye_conf, right_eye_conf;
    float left_eye_curve_score, right_eye_curve_score;
    
    // 车辆状态
    float vehicle_speed;
    int vehicle_gear;
    float steering_angle;
    
    // 标定进度信息
    int head_buffer_size;
    int left_eye_buffer_size;
    int right_eye_buffer_size;
    bool head_calibrated;
    bool left_eye_calibrated;
    bool right_eye_calibrated;
    
    // 构造函数
    CalibrationDebugRecord() : 
        timestamp(0), filter_reason(CalibrationFilterReason::UNKNOWN_REASON),
        head_yaw(0), head_pitch(0), head_roll(0),
        left_eye_yaw(0), left_eye_pitch(0), right_eye_yaw(0), right_eye_pitch(0),
        left_eye_conf(0), right_eye_conf(0),
        left_eye_curve_score(0), right_eye_curve_score(0),
        vehicle_speed(0), vehicle_gear(0), steering_angle(0),
        head_buffer_size(0), left_eye_buffer_size(0), right_eye_buffer_size(0),
        head_calibrated(false), left_eye_calibrated(false), right_eye_calibrated(false) {}
};

// 标定进度统计信息
struct CalibrationProgressStats {
    int total_frames_processed;
    int frames_accepted;
    std::map<CalibrationFilterReason, int> filter_reason_counts;
    
    // 各部分标定进度
    int head_target_frames;
    int left_eye_target_frames;
    int right_eye_target_frames;
    int head_current_frames;
    int left_eye_current_frames;
    int right_eye_current_frames;
    
    bool head_calibration_finished;
    bool left_eye_calibration_finished;
    bool right_eye_calibration_finished;
    
    CalibrationProgressStats() : 
        total_frames_processed(0), frames_accepted(0),
        head_target_frames(300), left_eye_target_frames(300), right_eye_target_frames(300),
        head_current_frames(0), left_eye_current_frames(0), right_eye_current_frames(0),
        head_calibration_finished(false), left_eye_calibration_finished(false), 
        right_eye_calibration_finished(false) {}
};

// 调试配置结构
struct CalibrationDebugConfig {
    bool enable;                    // 是否启用调试
    std::string log_level;          // 日志级别: "none", "summary", "detailed"
    int output_interval;            // 输出间隔(帧数)
    bool save_to_file;              // 是否保存到文件
    std::string log_file_path;      // 日志文件路径
    int max_records;                // 最大记录数(环形缓冲区大小)
    
    CalibrationDebugConfig() : 
        enable(false), log_level("summary"), output_interval(100),
        save_to_file(false), log_file_path("./calibration_debug.log"), max_records(1000) {}
};

// 标定调试器类(单例模式)
class CalibrationDebugger {
public:
    static CalibrationDebugger* getInstance();
    
    // 初始化和配置
    void initialize(const CalibrationDebugConfig& config);
    void loadConfigFromJson(const std::string& config_file);
    
    // 记录调试信息
    void recordFrame(const CalibrationDebugRecord& record);
    void updateProgress(int head_buffer_size, int left_eye_buffer_size, int right_eye_buffer_size,
                       bool head_calibrated, bool left_eye_calibrated, bool right_eye_calibrated);
    
    // 输出和报告
    void printRealTimeProgress();
    std::string generateProgressReport();
    std::string generateDetailedReport();
    void saveToFile();
    
    // 工具方法
    static std::string filterReasonToString(CalibrationFilterReason reason);
    static std::string generateProgressBar(int current, int total, int width = 10);
    
    // 清理和重置
    void reset();
    void cleanup();

private:
    CalibrationDebugger() = default;
    ~CalibrationDebugger() = default;
    CalibrationDebugger(const CalibrationDebugger&) = delete;
    CalibrationDebugger& operator=(const CalibrationDebugger&) = delete;
    
    static CalibrationDebugger* instance_;
    static std::mutex instance_mutex_;
    
    CalibrationDebugConfig config_;
    CalibrationProgressStats stats_;
    std::deque<CalibrationDebugRecord> records_;  // 环形缓冲区
    std::mutex records_mutex_;
    std::ofstream log_file_;
    
    // 内部方法
    void writeToFile(const std::string& content);
    void updateFilterReasonCounts(CalibrationFilterReason reason);
};

} // namespace tongxing

#endif // CALIBRATION_DEBUG_H
