CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2
INCLUDES = -I.
LIBS = -lpthread

SRCDIR = src/warming
SOURCES = $(SRCDIR)/calibration_debug.cpp test_calibration_debug.cpp
TARGET = test_calibration_debug

.PHONY: all clean test

all: $(TARGET)

$(TARGET): $(SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^ $(LIBS)

test: $(TARGET)
	./$(TARGET)

clean:
	rm -f $(TARGET) test_calibration_debug.log

install-deps:
	sudo apt-get update
	sudo apt-get install -y libjsoncpp-dev

help:
	@echo "Available targets:"
	@echo "  all          - Build the test program"
	@echo "  test         - Build and run the test program"
	@echo "  clean        - Remove built files and logs"
	@echo "  install-deps - Install required dependencies"
	@echo "  help         - Show this help message"
