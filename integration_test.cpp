#include "tongxing_util/src/util/CalibrationLogger.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace tongxing;

// 模拟TXCarInfo结构
struct MockTXCarInfo {
    float speed;
    int gear;
    float steer_whl_snsr_rad;
};

// 模拟TXDmsFaceInfo结构
struct MockTXDmsFaceInfo {
    float head_yaw;
    float head_pitch;
    float head_roll;
    struct {
        float pupil_score;
        float yaw;
        float pitch;
    } left_eye_landmark;
    struct {
        float pupil_score;
        float yaw;
        float pitch;
    } right_eye_landmark;
};

// 模拟DistractionWarn的标定方法
class MockDistractionWarn {
public:
    MockDistractionWarn() {
        initCalibrationLogger();
    }
    
    void initCalibrationLogger() {
        auto& logger = CalibrationLogger::getInstance();
        logger.setOutputInterval(1000); // 1秒间隔
        logger.setEnabled(true);
        
        // 添加控制台观察者
        auto console_observer = std::make_shared<ConsoleLogObserver>();
        logger.addObserver(console_observer);
        
        // 重置统计信息
        logger.resetStats();
        logger.setPhase(CalibrationPhase::PREPARING);
    }
    
    void auto_calibration_distraction(MockTXDmsFaceInfo face_info,
                                     const MockTXCarInfo* car_info,
                                     const float leye_uper_curve_score,
                                     const float reye_uper_curve_score,
                                     long now_ts) {
        // 获取日志系统实例
        auto& logger = CalibrationLogger::getInstance();
        
        // 记录车辆信息
        logger.recordVehicleInfo(car_info->speed, car_info->gear);
        
        // 记录标定进度信息
        int head_buffer_size = head_buffer_;
        int left_eye_buffer_size = left_eye_buffer_;
        int right_eye_buffer_size = right_eye_buffer_;
        
        // 更新标定阶段
        if (auto_calibration_) {
            logger.setPhase(CalibrationPhase::COMPLETED);
        } else {
            logger.setPhase(CalibrationPhase::IN_PROGRESS);
        }
        
        // 记录进度信息（这会触发汇总输出检查）
        logger.recordProgress(head_buffer_size, left_eye_buffer_size, right_eye_buffer_size);
        
        // 检查车速和档位
        if (car_info->speed < 20 || car_info->gear != 1) {
            // 记录过滤原因
            if (car_info->speed < 20) {
                logger.recordFilter("车速过低", "车速" + std::to_string(car_info->speed) + "km/h低于20km/h");
            } else if (car_info->gear != 1) {
                logger.recordFilter("档位无效", "档位" + std::to_string(car_info->gear) + "不是前进档");
            }
            return;
        }
        
        // 检查人脸角度
        if (face_info.head_yaw < -25 || face_info.head_yaw > 25 ||
            face_info.head_pitch < -20 || face_info.head_pitch > 20 ||
            face_info.head_roll < -15 || face_info.head_roll > 15) {
            
            std::string angle_type;
            float angle_value;
            if (face_info.head_yaw < -25 || face_info.head_yaw > 25) {
                angle_type = "yaw";
                angle_value = face_info.head_yaw;
            } else if (face_info.head_pitch < -20 || face_info.head_pitch > 20) {
                angle_type = "pitch";
                angle_value = face_info.head_pitch;
            } else {
                angle_type = "roll";
                angle_value = face_info.head_roll;
            }
            logger.recordFilter("人脸角度超范围", angle_type + "角度" + std::to_string(angle_value) + "超出配置范围");
            return;
        }
        
        // 模拟缓冲区增长
        head_buffer_++;
        left_eye_buffer_++;
        right_eye_buffer_++;
        
        // 检查是否标定完成
        if (head_buffer_ >= 300 && left_eye_buffer_ >= 280 && right_eye_buffer_ >= 280) {
            auto_calibration_ = true;
            logger.recordSuccess(true, true, true);
        }
    }
    
private:
    int head_buffer_ = 0;
    int left_eye_buffer_ = 0;
    int right_eye_buffer_ = 0;
    bool auto_calibration_ = false;
};

int main() {
    std::cout << "=== 集成测试：模拟实际标定过程 ===" << std::endl;
    
    MockDistractionWarn distraction_warn;
    
    // 模拟数据
    MockTXCarInfo car_info;
    MockTXDmsFaceInfo face_info;
    
    std::cout << "\n1. 测试车速过低场景" << std::endl;
    car_info = {15.0f, 1, 0.1f};
    face_info = {5.0f, 3.0f, 2.0f, {1.2f, 2.0f, 1.0f}, {1.1f, 1.5f, 0.8f}};
    
    for (int i = 0; i < 5; i++) {
        car_info.speed = 15.0f + i;
        distraction_warn.auto_calibration_distraction(face_info, &car_info, 0.8f, 0.9f, 
                                                     std::chrono::duration_cast<std::chrono::milliseconds>(
                                                         std::chrono::system_clock::now().time_since_epoch()).count());
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    std::this_thread::sleep_for(std::chrono::milliseconds(1200)); // 等待输出
    
    std::cout << "\n2. 测试正常标定过程" << std::endl;
    car_info = {25.0f, 1, 0.1f};
    face_info = {5.0f, 3.0f, 2.0f, {1.2f, 2.0f, 1.0f}, {1.1f, 1.5f, 0.8f}};
    
    for (int i = 0; i < 10; i++) {
        // 偶尔添加角度超范围的情况
        if (i == 3) {
            face_info.head_yaw = 30.0f; // 超出范围
        } else if (i == 6) {
            face_info.head_pitch = 25.0f; // 超出范围
        } else {
            face_info.head_yaw = 5.0f + i * 0.5f;
            face_info.head_pitch = 3.0f + i * 0.2f;
            face_info.head_roll = 2.0f + i * 0.1f;
        }
        
        distraction_warn.auto_calibration_distraction(face_info, &car_info, 0.8f, 0.9f, 
                                                     std::chrono::duration_cast<std::chrono::milliseconds>(
                                                         std::chrono::system_clock::now().time_since_epoch()).count());
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    std::this_thread::sleep_for(std::chrono::milliseconds(1200)); // 等待输出
    
    std::cout << "\n3. 继续标定直到完成" << std::endl;
    face_info = {5.0f, 3.0f, 2.0f, {1.2f, 2.0f, 1.0f}, {1.1f, 1.5f, 0.8f}};
    
    // 继续标定直到完成（需要大约290次）
    for (int i = 0; i < 300; i++) {
        distraction_warn.auto_calibration_distraction(face_info, &car_info, 0.8f, 0.9f, 
                                                     std::chrono::duration_cast<std::chrono::milliseconds>(
                                                         std::chrono::system_clock::now().time_since_epoch()).count());
        
        // 每50次等待一下，让日志输出
        if (i % 50 == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1200));
        } else {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
    
    std::this_thread::sleep_for(std::chrono::milliseconds(1200)); // 等待最终输出
    
    std::cout << "\n=== 集成测试完成 ===" << std::endl;
    
    return 0;
}
