#include "tongxing_util/src/util/CalibrationLogger.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace tongxing;

int main() {
    std::cout << "=== 标定日志系统验证测试 ===" << std::endl;
    
    auto& logger = CalibrationLogger::getInstance();
    
    // 设置输出间隔为500ms，便于快速测试
    logger.setOutputInterval(500);
    logger.setEnabled(true);
    
    // 添加控制台观察者
    auto console_observer = std::make_shared<ConsoleLogObserver>();
    logger.addObserver(console_observer);
    
    // 重置统计信息
    logger.resetStats();
    
    std::cout << "\n1. 测试准备阶段 - 车速过低过滤" << std::endl;
    logger.setPhase(CalibrationPhase::PREPARING);
    logger.recordVehicleInfo(0.0f, 0);
    
    // 模拟车速过低过滤
    for (int i = 0; i < 5; i++) {
        logger.recordFilter("车速过低", "车速" + std::to_string(i * 3 + 10) + "km/h低于20km/h");
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 等待输出
    std::this_thread::sleep_for(std::chrono::milliseconds(600));
    
    std::cout << "\n2. 测试进行阶段 - 缓冲区填充" << std::endl;
    logger.setPhase(CalibrationPhase::IN_PROGRESS);
    logger.recordVehicleInfo(25.0f, 1);
    
    // 模拟缓冲区填充过程
    for (int i = 1; i <= 8; i++) {
        int head_buffer = i * 40;
        int left_eye_buffer = i * 35;
        int right_eye_buffer = i * 38;
        
        logger.recordProgress(head_buffer, left_eye_buffer, right_eye_buffer);
        
        // 偶尔添加过滤事件
        if (i % 2 == 0) {
            logger.recordFilter("人脸角度超范围", "yaw角度" + std::to_string(20 + i * 2) + "超出配置范围");
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    // 等待输出
    std::this_thread::sleep_for(std::chrono::milliseconds(600));
    
    std::cout << "\n3. 测试标定完成" << std::endl;
    logger.recordSuccess(true, true, true);
    
    // 强制输出最终统计信息
    logger.flushStats();
    
    std::cout << "\n4. 测试过滤原因统计" << std::endl;
    logger.resetStats();
    
    // 模拟多种过滤原因
    logger.recordFilter("车速过低", "车速15km/h低于20km/h");
    logger.recordFilter("车速过低", "车速18km/h低于20km/h");
    logger.recordFilter("档位无效", "档位0不是前进档");
    logger.recordFilter("人脸角度超范围", "yaw角度25超出配置范围");
    logger.recordFilter("人脸角度超范围", "pitch角度30超出配置范围");
    logger.recordFilter("人脸角度超范围", "roll角度15超出配置范围");
    logger.recordFilter("方向盘角度无效", "方向盘转角0.8超出配置范围");
    
    // 强制输出统计信息
    logger.flushStats();
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    return 0;
}
